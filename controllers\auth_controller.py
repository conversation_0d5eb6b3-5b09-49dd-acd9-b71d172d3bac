"""
تحكم المصادقة والجلسات
"""
from datetime import datetime, timedelta
from models.user import User
from models.audit_log import AuditLog

class AuthController:
    """تحكم المصادقة"""
    
    def __init__(self):
        self.current_user = None
        self.session_timeout = 60  # دقيقة
        self.last_activity = None
        self.login_attempts = {}
        self.max_attempts = 5
    
    def login(self, username, password, remember_me=False):
        """تسجيل الدخول"""
        # فحص محاولات تسجيل الدخول
        if self._is_account_locked(username):
            return False, "الحساب مقفل مؤقتاً بسبب محاولات دخول خاطئة متعددة"
        
        # محاولة المصادقة
        user = User.authenticate(username, password)
        
        if user:
            # نجح تسجيل الدخول
            self.current_user = user
            self.last_activity = datetime.now()
            
            # إعادة تعيين محاولات تسجيل الدخول
            if username in self.login_attempts:
                del self.login_attempts[username]
            
            # تسجيل النشاط
            AuditLog.log_activity(
                user_id=user.id,
                action='LOGIN',
                table_name='users',
                record_id=user.id
            )
            
            return True, "تم تسجيل الدخول بنجاح"
        
        else:
            # فشل تسجيل الدخول
            self._record_failed_attempt(username)
            
            # تسجيل محاولة الدخول الفاشلة
            AuditLog.log_activity(
                user_id=None,
                action='LOGIN_FAILED',
                table_name='users',
                old_values=f"Username: {username}"
            )
            
            return False, "اسم المستخدم أو كلمة المرور غير صحيحة"
    
    def logout(self):
        """تسجيل الخروج"""
        if self.current_user:
            # تسجيل النشاط
            AuditLog.log_activity(
                user_id=self.current_user.id,
                action='LOGOUT',
                table_name='users',
                record_id=self.current_user.id
            )
            
            self.current_user = None
            self.last_activity = None
            return True
        
        return False
    
    def is_authenticated(self):
        """فحص ما إذا كان المستخدم مسجل الدخول"""
        if not self.current_user:
            return False
        
        # فحص انتهاء الجلسة
        if self._is_session_expired():
            self.logout()
            return False
        
        return True
    
    def check_permission(self, permission):
        """فحص الصلاحية"""
        if not self.is_authenticated():
            return False
        
        return self.current_user.has_permission(permission)
    
    def require_permission(self, permission):
        """طلب صلاحية معينة"""
        if not self.check_permission(permission):
            raise PermissionError(f"ليس لديك صلاحية: {permission}")
    
    def update_activity(self):
        """تحديث وقت آخر نشاط"""
        if self.current_user:
            self.last_activity = datetime.now()
    
    def get_session_remaining_time(self):
        """الحصول على الوقت المتبقي للجلسة"""
        if not self.current_user or not self.last_activity:
            return 0
        
        elapsed = datetime.now() - self.last_activity
        remaining = timedelta(minutes=self.session_timeout) - elapsed
        
        return max(0, remaining.total_seconds())
    
    def extend_session(self):
        """تمديد الجلسة"""
        if self.current_user:
            self.last_activity = datetime.now()
            return True
        return False
    
    def change_password(self, old_password, new_password):
        """تغيير كلمة المرور"""
        if not self.current_user:
            return False, "يجب تسجيل الدخول أولاً"
        
        # التحقق من كلمة المرور القديمة
        if not self.current_user.check_password(old_password):
            return False, "كلمة المرور القديمة غير صحيحة"
        
        # التحقق من قوة كلمة المرور الجديدة
        is_valid, message = self._validate_password(new_password)
        if not is_valid:
            return False, message
        
        # تحديث كلمة المرور
        self.current_user.set_password(new_password)
        success = self.current_user.update()
        
        if success:
            # تسجيل النشاط
            AuditLog.log_activity(
                user_id=self.current_user.id,
                action='PASSWORD_CHANGE',
                table_name='users',
                record_id=self.current_user.id
            )
            
            return True, "تم تغيير كلمة المرور بنجاح"
        
        return False, "فشل في تغيير كلمة المرور"
    
    def reset_password(self, username, new_password):
        """إعادة تعيين كلمة المرور (للمدير)"""
        if not self.current_user or not self.current_user.is_admin():
            return False, "ليس لديك صلاحية لإعادة تعيين كلمة المرور"
        
        user = User.find_by_username(username)
        if not user:
            return False, "المستخدم غير موجود"
        
        # التحقق من قوة كلمة المرور
        is_valid, message = self._validate_password(new_password)
        if not is_valid:
            return False, message
        
        # تحديث كلمة المرور
        user.set_password(new_password)
        success = user.update()
        
        if success:
            # تسجيل النشاط
            AuditLog.log_activity(
                user_id=self.current_user.id,
                action='PASSWORD_RESET',
                table_name='users',
                record_id=user.id,
                new_values=f"Reset password for user: {user.username}"
            )
            
            return True, "تم إعادة تعيين كلمة المرور بنجاح"
        
        return False, "فشل في إعادة تعيين كلمة المرور"
    
    def _is_session_expired(self):
        """فحص انتهاء الجلسة"""
        if not self.last_activity:
            return True
        
        elapsed = datetime.now() - self.last_activity
        return elapsed.total_seconds() > (self.session_timeout * 60)
    
    def _is_account_locked(self, username):
        """فحص ما إذا كان الحساب مقفل"""
        if username not in self.login_attempts:
            return False
        
        attempts = self.login_attempts[username]
        
        # فحص عدد المحاولات
        if attempts['count'] < self.max_attempts:
            return False
        
        # فحص وقت آخر محاولة
        time_since_last = datetime.now() - attempts['last_attempt']
        lockout_duration = timedelta(minutes=15)  # قفل لمدة 15 دقيقة
        
        if time_since_last > lockout_duration:
            # انتهت فترة القفل
            del self.login_attempts[username]
            return False
        
        return True
    
    def _record_failed_attempt(self, username):
        """تسجيل محاولة دخول فاشلة"""
        now = datetime.now()
        
        if username not in self.login_attempts:
            self.login_attempts[username] = {
                'count': 1,
                'first_attempt': now,
                'last_attempt': now
            }
        else:
            attempts = self.login_attempts[username]
            
            # إعادة تعيين العداد إذا مر وقت طويل
            time_since_first = now - attempts['first_attempt']
            if time_since_first > timedelta(hours=1):
                attempts['count'] = 1
                attempts['first_attempt'] = now
            else:
                attempts['count'] += 1
            
            attempts['last_attempt'] = now
    
    def _validate_password(self, password):
        """التحقق من قوة كلمة المرور"""
        if not password:
            return False, "كلمة المرور مطلوبة"
        
        if len(password) < 6:
            return False, "كلمة المرور يجب أن تكون 6 أحرف على الأقل"
        
        # يمكن إضافة المزيد من قواعد التحقق هنا
        # مثل وجود أرقام وأحرف كبيرة وصغيرة ورموز خاصة
        
        return True, ""
    
    def get_user_permissions(self):
        """الحصول على صلاحيات المستخدم الحالي"""
        if not self.current_user:
            return []
        
        return self.current_user.get_permissions_list()
    
    def can_access_module(self, module_name):
        """فحص إمكانية الوصول لوحدة معينة"""
        if not self.current_user:
            return False
        
        return self.current_user.can_access_module(module_name)
    
    def get_accessible_modules(self):
        """الحصول على الوحدات التي يمكن للمستخدم الوصول إليها"""
        if not self.current_user:
            return []
        
        all_modules = [
            'dashboard', 'purchasing', 'suppliers', 'customers', 'sales',
            'inventory', 'accounting', 'reports', 'invoices', 'employees',
            'users', 'notifications', 'audit_log', 'backup', 'settings', 'branches'
        ]
        
        accessible = []
        for module in all_modules:
            if self.current_user.can_access_module(module):
                accessible.append(module)
        
        return accessible

# إنشاء مثيل واحد من تحكم المصادقة
auth_controller = AuthController()
