"""
نظام السجلات
"""
import logging
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler
from config.settings import LOGS_DIR, LOGGING_CONFIG

class Logger:
    """فئة إدارة السجلات"""
    
    def __init__(self, name="ERP_System"):
        self.name = name
        self.logger = logging.getLogger(name)
        self.setup_logger()
    
    def setup_logger(self):
        """إعداد نظام السجلات"""
        # تجنب إعداد السجل أكثر من مرة
        if self.logger.handlers:
            return
        
        # تعيين مستوى السجل
        level = getattr(logging, LOGGING_CONFIG['level'], logging.INFO)
        self.logger.setLevel(level)
        
        # إنشاء مجلد السجلات إذا لم يكن موجوداً
        os.makedirs(LOGS_DIR, exist_ok=True)
        
        # إعداد تنسيق السجل
        formatter = logging.Formatter(
            LOGGING_CONFIG['format'],
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # سجل الملف العام
        self.setup_file_handler('app.log', formatter, level)
        
        # سجل الأخطاء
        self.setup_file_handler('errors.log', formatter, logging.ERROR)
        
        # سجل وحدة التحكم (للتطوير)
        self.setup_console_handler(formatter)
    
    def setup_file_handler(self, filename, formatter, level):
        """إعداد معالج ملف السجل"""
        filepath = os.path.join(LOGS_DIR, filename)
        
        # استخدام RotatingFileHandler لتدوير السجلات
        handler = RotatingFileHandler(
            filepath,
            maxBytes=LOGGING_CONFIG['max_file_size_mb'] * 1024 * 1024,
            backupCount=LOGGING_CONFIG['backup_count'],
            encoding='utf-8'
        )
        
        handler.setLevel(level)
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def setup_console_handler(self, formatter):
        """إعداد معالج وحدة التحكم"""
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def info(self, message, extra=None):
        """تسجيل رسالة معلومات"""
        self.logger.info(message, extra=extra)
    
    def warning(self, message, extra=None):
        """تسجيل رسالة تحذير"""
        self.logger.warning(message, extra=extra)
    
    def error(self, message, extra=None):
        """تسجيل رسالة خطأ"""
        self.logger.error(message, extra=extra)
    
    def critical(self, message, extra=None):
        """تسجيل رسالة خطأ حرج"""
        self.logger.critical(message, extra=extra)
    
    def debug(self, message, extra=None):
        """تسجيل رسالة تطوير"""
        self.logger.debug(message, extra=extra)
    
    def log_user_activity(self, user_id, action, details=""):
        """تسجيل نشاط المستخدم"""
        message = f"User {user_id}: {action}"
        if details:
            message += f" - {details}"
        
        self.info(message, extra={
            'user_id': user_id,
            'action': action,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
    
    def log_database_operation(self, operation, table, record_id=None, details=""):
        """تسجيل عملية قاعدة البيانات"""
        message = f"DB {operation}: {table}"
        if record_id:
            message += f" (ID: {record_id})"
        if details:
            message += f" - {details}"
        
        self.info(message, extra={
            'operation': operation,
            'table': table,
            'record_id': record_id,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
    
    def log_system_event(self, event, details=""):
        """تسجيل حدث النظام"""
        message = f"System Event: {event}"
        if details:
            message += f" - {details}"
        
        self.info(message, extra={
            'event': event,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
    
    def log_error_with_context(self, error, context="", user_id=None):
        """تسجيل خطأ مع السياق"""
        message = f"Error: {str(error)}"
        if context:
            message = f"{context} - {message}"
        
        self.error(message, extra={
            'error': str(error),
            'context': context,
            'user_id': user_id,
            'timestamp': datetime.now().isoformat()
        })
    
    def log_security_event(self, event, user_id=None, ip_address=None, details=""):
        """تسجيل حدث أمني"""
        message = f"Security Event: {event}"
        if user_id:
            message += f" (User: {user_id})"
        if ip_address:
            message += f" (IP: {ip_address})"
        if details:
            message += f" - {details}"
        
        self.warning(message, extra={
            'event': event,
            'user_id': user_id,
            'ip_address': ip_address,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
    
    def log_performance(self, operation, duration, details=""):
        """تسجيل أداء العمليات"""
        message = f"Performance: {operation} took {duration:.2f}s"
        if details:
            message += f" - {details}"
        
        # تسجيل كتحذير إذا كانت العملية بطيئة
        if duration > 5.0:
            self.warning(message)
        else:
            self.info(message)
    
    @staticmethod
    def get_log_files():
        """الحصول على قائمة ملفات السجلات"""
        log_files = []
        
        if os.path.exists(LOGS_DIR):
            for filename in os.listdir(LOGS_DIR):
                if filename.endswith('.log'):
                    filepath = os.path.join(LOGS_DIR, filename)
                    file_info = {
                        'name': filename,
                        'path': filepath,
                        'size': os.path.getsize(filepath),
                        'modified': datetime.fromtimestamp(os.path.getmtime(filepath))
                    }
                    log_files.append(file_info)
        
        return sorted(log_files, key=lambda x: x['modified'], reverse=True)
    
    @staticmethod
    def read_log_file(filename, lines=100):
        """قراءة ملف السجل"""
        filepath = os.path.join(LOGS_DIR, filename)
        
        if not os.path.exists(filepath):
            return []
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                return all_lines[-lines:] if lines else all_lines
        except Exception as e:
            return [f"Error reading log file: {str(e)}"]
    
    @staticmethod
    def clear_log_file(filename):
        """مسح ملف السجل"""
        filepath = os.path.join(LOGS_DIR, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write("")
            return True
        except Exception:
            return False
    
    @staticmethod
    def delete_old_logs(days=30):
        """حذف السجلات القديمة"""
        if not os.path.exists(LOGS_DIR):
            return 0
        
        deleted_count = 0
        cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)
        
        for filename in os.listdir(LOGS_DIR):
            if filename.endswith('.log'):
                filepath = os.path.join(LOGS_DIR, filename)
                
                try:
                    if os.path.getmtime(filepath) < cutoff_time:
                        os.remove(filepath)
                        deleted_count += 1
                except Exception:
                    continue
        
        return deleted_count

# إنشاء مثيل واحد من نظام السجلات
system_logger = Logger("ERP_System")

# دوال مختصرة للاستخدام السريع
def log_info(message, extra=None):
    """تسجيل معلومات"""
    system_logger.info(message, extra)

def log_warning(message, extra=None):
    """تسجيل تحذير"""
    system_logger.warning(message, extra)

def log_error(message, extra=None):
    """تسجيل خطأ"""
    system_logger.error(message, extra)

def log_user_activity(user_id, action, details=""):
    """تسجيل نشاط المستخدم"""
    system_logger.log_user_activity(user_id, action, details)

def log_database_operation(operation, table, record_id=None, details=""):
    """تسجيل عملية قاعدة البيانات"""
    system_logger.log_database_operation(operation, table, record_id, details)

def log_security_event(event, user_id=None, ip_address=None, details=""):
    """تسجيل حدث أمني"""
    system_logger.log_security_event(event, user_id, ip_address, details)
