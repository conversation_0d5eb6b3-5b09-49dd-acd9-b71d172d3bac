"""
شاشة تسجيل الدخول
"""
import customtkinter as ctk
from tkinter import messagebox
import tkinter as tk
from config.languages import get_text, get_current_language, set_language
from utils.rtl_support import R<PERSON>Frame, RTLLabel, RTLButton, RTLEntry
from controllers.auth_controller import auth_controller

class LoginView(ctk.CTk):
    """شاشة تسجيل الدخول"""

    def __init__(self):
        super().__init__()

        self.current_language = get_current_language()

        # إعداد النافذة
        self.setup_window()

        # إنشاء الواجهة
        self.create_widgets()

        # تطبيق الترجمة
        self.apply_translations()

        # ربط الأحداث
        self.bind_events()

        # التركيز على حقل اسم المستخدم
        self.username_entry.focus()

    def setup_window(self):
        """إعداد النافذة"""
        self.title(get_text('login', self.current_language))
        self.geometry("450x600")
        self.resizable(False, False)

        # توسيط النافذة
        self.center_window()

        # إعداد المظهر
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")

        # تعيين الأيقونة
        try:
            self.iconbitmap("assets/icons/app_icon.ico")
        except:
            pass

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = 450
        height = 600

        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()

        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        self.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = RTLFrame(self, language=self.current_language)
        main_frame.pack(fill="both", expand=True, padx=30, pady=30)

        # شعار التطبيق
        self.create_logo_section(main_frame)

        # عنوان تسجيل الدخول
        self.create_title_section(main_frame)

        # نموذج تسجيل الدخول
        self.create_login_form(main_frame)

        # أزرار تسجيل الدخول
        self.create_buttons_section(main_frame)

        # قسم اللغة
        self.create_language_section(main_frame)

        # معلومات التطبيق
        self.create_info_section(main_frame)

    def create_logo_section(self, parent):
        """إنشاء قسم الشعار"""
        logo_frame = RTLFrame(parent, language=self.current_language)
        logo_frame.pack(pady=(0, 20))

        # يمكن إضافة شعار الشركة هنا
        try:
            # محاولة تحميل الشعار
            logo_image = ctk.CTkImage(
                light_image=tk.PhotoImage(file="assets/images/logo.png"),
                size=(100, 100)
            )
            logo_label = ctk.CTkLabel(logo_frame, image=logo_image, text="")
            logo_label.pack()
        except:
            # إذا لم يكن الشعار متوفراً، عرض نص بديل
            logo_label = RTLLabel(
                logo_frame,
                text="🏢",
                language=self.current_language,
                font=ctk.CTkFont(size=60)
            )
            logo_label.pack()

    def create_title_section(self, parent):
        """إنشاء قسم العنوان"""
        title_frame = RTLFrame(parent, language=self.current_language)
        title_frame.pack(pady=(0, 30))

        # عنوان التطبيق
        self.app_title_label = RTLLabel(
            title_frame,
            text=get_text('app_title', self.current_language),
            language=self.current_language,
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.app_title_label.pack(pady=(0, 10))

        # عنوان تسجيل الدخول
        self.login_title_label = RTLLabel(
            title_frame,
            text=get_text('login', self.current_language),
            language=self.current_language,
            font=ctk.CTkFont(size=18)
        )
        self.login_title_label.pack()

    def create_login_form(self, parent):
        """إنشاء نموذج تسجيل الدخول"""
        form_frame = RTLFrame(parent, language=self.current_language)
        form_frame.pack(fill="x", pady=(0, 20))

        # حقل اسم المستخدم
        username_frame = RTLFrame(form_frame, language=self.current_language)
        username_frame.pack(fill="x", pady=(0, 15))

        self.username_label = RTLLabel(
            username_frame,
            text=get_text('username', self.current_language),
            language=self.current_language,
            font=ctk.CTkFont(size=14)
        )
        self.username_label.pack(anchor="w" if self.current_language == "en" else "e", pady=(0, 5))

        self.username_entry = RTLEntry(
            username_frame,
            language=self.current_language,
            placeholder_text=get_text('username', self.current_language),
            height=40,
            font=ctk.CTkFont(size=14)
        )
        self.username_entry.pack(fill="x")

        # حقل كلمة المرور
        password_frame = RTLFrame(form_frame, language=self.current_language)
        password_frame.pack(fill="x", pady=(0, 15))

        self.password_label = RTLLabel(
            password_frame,
            text=get_text('password', self.current_language),
            language=self.current_language,
            font=ctk.CTkFont(size=14)
        )
        self.password_label.pack(anchor="w" if self.current_language == "en" else "e", pady=(0, 5))

        self.password_entry = RTLEntry(
            password_frame,
            language=self.current_language,
            placeholder_text=get_text('password', self.current_language),
            show="*",
            height=40,
            font=ctk.CTkFont(size=14)
        )
        self.password_entry.pack(fill="x")

        # خيار تذكرني
        remember_frame = RTLFrame(form_frame, language=self.current_language)
        remember_frame.pack(fill="x", pady=(10, 0))

        self.remember_var = ctk.BooleanVar()
        self.remember_checkbox = ctk.CTkCheckBox(
            remember_frame,
            text=get_text('remember_me', self.current_language),
            variable=self.remember_var,
            font=ctk.CTkFont(size=12)
        )

        if self.current_language == "ar":
            self.remember_checkbox.pack(anchor="e")
        else:
            self.remember_checkbox.pack(anchor="w")

    def create_buttons_section(self, parent):
        """إنشاء قسم الأزرار"""
        buttons_frame = RTLFrame(parent, language=self.current_language)
        buttons_frame.pack(fill="x", pady=(20, 0))

        # زر تسجيل الدخول
        self.login_button = RTLButton(
            buttons_frame,
            text=get_text('login', self.current_language),
            language=self.current_language,
            command=self.login,
            height=45,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.login_button.pack(fill="x", pady=(0, 10))

        # رابط نسيت كلمة المرور
        self.forgot_password_label = RTLLabel(
            buttons_frame,
            text=get_text('forgot_password', self.current_language),
            language=self.current_language,
            font=ctk.CTkFont(size=12),
            text_color=("blue", "lightblue"),
            cursor="hand2"
        )
        self.forgot_password_label.pack()
        self.forgot_password_label.bind("<Button-1>", self.forgot_password)

    def create_language_section(self, parent):
        """إنشاء قسم اختيار اللغة"""
        language_frame = RTLFrame(parent, language=self.current_language)
        language_frame.pack(fill="x", pady=(30, 0))

        # تسمية اللغة
        language_label = RTLLabel(
            language_frame,
            text=get_text('language', self.current_language),
            language=self.current_language,
            font=ctk.CTkFont(size=12)
        )
        language_label.pack(pady=(0, 5))

        # أزرار اللغة
        lang_buttons_frame = RTLFrame(language_frame, language=self.current_language)
        lang_buttons_frame.pack()

        # زر العربية
        self.arabic_button = ctk.CTkButton(
            lang_buttons_frame,
            text="العربية",
            command=lambda: self.change_language("ar"),
            width=80,
            height=30,
            font=ctk.CTkFont(size=12),
            fg_color="transparent" if self.current_language != "ar" else None,
            text_color=("gray", "white") if self.current_language != "ar" else ("white", "white")
        )
        self.arabic_button.pack(side="right" if self.current_language == "ar" else "left", padx=5)

        # زر الإنجليزية
        self.english_button = ctk.CTkButton(
            lang_buttons_frame,
            text="English",
            command=lambda: self.change_language("en"),
            width=80,
            height=30,
            font=ctk.CTkFont(size=12),
            fg_color="transparent" if self.current_language != "en" else None,
            text_color=("gray", "white") if self.current_language != "en" else ("white", "white")
        )
        self.english_button.pack(side="left" if self.current_language == "ar" else "right", padx=5)

    def create_info_section(self, parent):
        """إنشاء قسم معلومات التطبيق"""
        info_frame = RTLFrame(parent, language=self.current_language)
        info_frame.pack(side="bottom", fill="x", pady=(20, 0))

        # معلومات الإصدار
        version_label = RTLLabel(
            info_frame,
            text="الإصدار 1.0.0",
            language=self.current_language,
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        version_label.pack()

        # حقوق الطبع
        copyright_label = RTLLabel(
            info_frame,
            text="© 2024 جميع الحقوق محفوظة",
            language=self.current_language,
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        copyright_label.pack()

    def apply_translations(self):
        """تطبيق الترجمات"""
        # تحديث النصوص
        self.title(get_text('login', self.current_language))

        if hasattr(self, 'app_title_label'):
            self.app_title_label.set_text(get_text('app_title', self.current_language))

        if hasattr(self, 'login_title_label'):
            self.login_title_label.set_text(get_text('login', self.current_language))

        if hasattr(self, 'username_label'):
            self.username_label.set_text(get_text('username', self.current_language))

        if hasattr(self, 'password_label'):
            self.password_label.set_text(get_text('password', self.current_language))

        if hasattr(self, 'login_button'):
            self.login_button.set_text(get_text('login', self.current_language))

        if hasattr(self, 'remember_checkbox'):
            self.remember_checkbox.configure(text=get_text('remember_me', self.current_language))

        if hasattr(self, 'forgot_password_label'):
            self.forgot_password_label.set_text(get_text('forgot_password', self.current_language))

    def bind_events(self):
        """ربط الأحداث"""
        # Enter للتسجيل
        self.username_entry.bind("<Return>", lambda e: self.password_entry.focus())
        self.password_entry.bind("<Return>", lambda e: self.login())

        # Escape للخروج
        self.bind("<Escape>", lambda e: self.quit())

        # تحديث النشاط عند الكتابة
        self.username_entry.bind("<KeyPress>", self.on_key_press)
        self.password_entry.bind("<KeyPress>", self.on_key_press)

    def on_key_press(self, event):
        """حدث الضغط على مفتاح"""
        # تحديث نشاط المستخدم
        auth_controller.update_activity()

    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        remember_me = self.remember_var.get()

        # التحقق من الحقول المطلوبة
        if not username:
            self.show_error(get_text('username', self.current_language) + " " + get_text('required_field', self.current_language))
            self.username_entry.focus()
            return

        if not password:
            self.show_error(get_text('password', self.current_language) + " " + get_text('required_field', self.current_language))
            self.password_entry.focus()
            return

        # تعطيل زر تسجيل الدخول مؤقتاً
        self.login_button.configure(state="disabled", text=get_text('loading', self.current_language))
        self.update()

        try:
            # محاولة تسجيل الدخول
            success, message = auth_controller.login(username, password, remember_me)

            if success:
                # نجح تسجيل الدخول
                self.show_success(message)

                # إخفاء نافذة تسجيل الدخول
                self.withdraw()

                # فتح النافذة الرئيسية
                self.open_main_window()
            else:
                # فشل تسجيل الدخول
                self.show_error(message)
                self.password_entry.delete(0, 'end')
                self.password_entry.focus()

        except Exception as e:
            self.show_error(f"خطأ في تسجيل الدخول: {str(e)}")

        finally:
            # إعادة تفعيل زر تسجيل الدخول
            self.login_button.configure(state="normal", text=get_text('login', self.current_language))

    def open_main_window(self):
        """فتح النافذة الرئيسية"""
        try:
            from views.dashboard_view import DashboardView

            # إنشاء النافذة الرئيسية
            main_window = DashboardView()
            main_window.protocol("WM_DELETE_WINDOW", self.on_main_window_close)

            # إخفاء نافذة تسجيل الدخول
            self.withdraw()

            # عرض النافذة الرئيسية
            main_window.mainloop()

        except Exception as e:
            self.show_error(f"خطأ في فتح النافذة الرئيسية: {str(e)}")
            self.deiconify()  # إظهار نافذة تسجيل الدخول مرة أخرى

    def on_main_window_close(self):
        """حدث إغلاق النافذة الرئيسية"""
        # تسجيل الخروج
        auth_controller.logout()

        # إظهار نافذة تسجيل الدخول مرة أخرى
        self.deiconify()

        # مسح الحقول
        self.username_entry.delete(0, 'end')
        self.password_entry.delete(0, 'end')
        self.remember_var.set(False)

        # التركيز على حقل اسم المستخدم
        self.username_entry.focus()

    def forgot_password(self, event=None):
        """نسيت كلمة المرور"""
        messagebox.showinfo(
            get_text('info', self.current_language),
            "يرجى الاتصال بمدير النظام لإعادة تعيين كلمة المرور"
        )

    def change_language(self, language):
        """تغيير اللغة"""
        if language != self.current_language:
            # حفظ اللغة الجديدة
            set_language(language)
            self.current_language = language

            # تطبيق الترجمات
            self.apply_translations()

            # إعادة إنشاء الواجهة لتطبيق اتجاه النص
            self.recreate_ui()

    def recreate_ui(self):
        """إعادة إنشاء الواجهة"""
        # حفظ القيم الحالية
        username = self.username_entry.get() if hasattr(self, 'username_entry') else ""
        password = self.password_entry.get() if hasattr(self, 'password_entry') else ""
        remember = self.remember_var.get() if hasattr(self, 'remember_var') else False

        # مسح الواجهة الحالية
        for widget in self.winfo_children():
            widget.destroy()

        # إعادة إنشاء الواجهة
        self.create_widgets()
        self.apply_translations()
        self.bind_events()

        # استعادة القيم
        if username:
            self.username_entry.insert(0, username)
        if password:
            self.password_entry.insert(0, password)
        self.remember_var.set(remember)

        # التركيز على الحقل المناسب
        if username and not password:
            self.password_entry.focus()
        else:
            self.username_entry.focus()

    def show_error(self, message):
        """عرض رسالة خطأ"""
        messagebox.showerror(
            get_text('error', self.current_language),
            message
        )

    def show_success(self, message):
        """عرض رسالة نجاح"""
        messagebox.showinfo(
            get_text('success', self.current_language),
            message
        )

    def show_info(self, message):
        """عرض رسالة معلومات"""
        messagebox.showinfo(
            get_text('info', self.current_language),
            message
        )
