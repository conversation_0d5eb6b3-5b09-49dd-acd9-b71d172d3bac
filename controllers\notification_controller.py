"""
تحكم الإشعارات
"""
from datetime import datetime, timedelta
from models.base_model import BaseModel

class Notification(BaseModel):
    """نموذج الإشعار"""
    
    table_name = "notifications"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_id = kwargs.get('user_id')
        self.title = kwargs.get('title', '')
        self.message = kwargs.get('message', '')
        self.notification_type = kwargs.get('notification_type', 'info')
        self.is_read = kwargs.get('is_read', False)

class NotificationController:
    """تحكم الإشعارات"""
    
    def __init__(self):
        pass
    
    def create_notification(self, user_id, title, message, notification_type='info'):
        """إنشاء إشعار جديد"""
        try:
            notification = Notification(
                user_id=user_id,
                title=title,
                message=message,
                notification_type=notification_type
            )
            
            if notification.create():
                return True, "تم إنشاء الإشعار بنجاح"
            else:
                return False, "فشل في إنشاء الإشعار"
        
        except Exception as e:
            return False, f"خطأ في إنشاء الإشعار: {str(e)}"
    
    def get_user_notifications(self, user_id, unread_only=False, limit=50):
        """الحصول على إشعارات المستخدم"""
        try:
            where_clause = "user_id = ?"
            params = [user_id]
            
            if unread_only:
                where_clause += " AND is_read = 0"
            
            notifications = Notification.find_all(
                where_clause, 
                params, 
                order_by="created_at DESC", 
                limit=limit
            )
            
            return notifications
        
        except Exception as e:
            print(f"Error getting user notifications: {e}")
            return []
    
    def mark_as_read(self, notification_id, user_id=None):
        """تحديد الإشعار كمقروء"""
        try:
            notification = Notification.find_by_id(notification_id)
            
            if not notification:
                return False, "الإشعار غير موجود"
            
            # التحقق من الصلاحية
            if user_id and notification.user_id != user_id:
                return False, "ليس لديك صلاحية لتحديث هذا الإشعار"
            
            notification.is_read = True
            
            if notification.update():
                return True, "تم تحديد الإشعار كمقروء"
            else:
                return False, "فشل في تحديث الإشعار"
        
        except Exception as e:
            return False, f"خطأ في تحديث الإشعار: {str(e)}"
    
    def mark_all_as_read(self, user_id):
        """تحديد جميع الإشعارات كمقروءة"""
        try:
            conn = Notification.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE notifications 
                SET is_read = 1 
                WHERE user_id = ? AND is_read = 0
            """, (user_id,))
            
            updated_count = cursor.rowcount
            conn.commit()
            conn.close()
            
            return True, f"تم تحديد {updated_count} إشعار كمقروء"
        
        except Exception as e:
            return False, f"خطأ في تحديث الإشعارات: {str(e)}"
    
    def delete_notification(self, notification_id, user_id=None):
        """حذف إشعار"""
        try:
            notification = Notification.find_by_id(notification_id)
            
            if not notification:
                return False, "الإشعار غير موجود"
            
            # التحقق من الصلاحية
            if user_id and notification.user_id != user_id:
                return False, "ليس لديك صلاحية لحذف هذا الإشعار"
            
            if notification.delete():
                return True, "تم حذف الإشعار"
            else:
                return False, "فشل في حذف الإشعار"
        
        except Exception as e:
            return False, f"خطأ في حذف الإشعار: {str(e)}"
    
    def get_unread_count(self, user_id):
        """الحصول على عدد الإشعارات غير المقروءة"""
        try:
            return Notification.count("user_id = ? AND is_read = 0", [user_id])
        except Exception as e:
            print(f"Error getting unread count: {e}")
            return 0
    
    def create_system_notification(self, title, message, notification_type='info', all_users=False, user_ids=None):
        """إنشاء إشعار نظام"""
        try:
            if all_users:
                # إرسال لجميع المستخدمين النشطين
                from models.user import User
                users = User.get_active_users()
                user_ids = [user.id for user in users]
            
            if not user_ids:
                return False, "لا توجد مستخدمين لإرسال الإشعار إليهم"
            
            success_count = 0
            
            for user_id in user_ids:
                success, _ = self.create_notification(user_id, title, message, notification_type)
                if success:
                    success_count += 1
            
            return True, f"تم إرسال الإشعار إلى {success_count} مستخدم"
        
        except Exception as e:
            return False, f"خطأ في إنشاء إشعار النظام: {str(e)}"
    
    def create_low_stock_notifications(self):
        """إنشاء إشعارات المخزون المنخفض"""
        try:
            from models.product import Product
            
            # الحصول على المنتجات منخفضة المخزون
            low_stock_products = Product.get_low_stock_products()
            
            if not low_stock_products:
                return True, "لا توجد منتجات منخفضة المخزون"
            
            # إنشاء إشعار لكل منتج
            for product in low_stock_products:
                title = "تحذير: مخزون منخفض"
                message = f"المنتج '{product.name}' منخفض المخزون. الكمية الحالية: {product.current_stock}"
                
                # إرسال للمدراء والمحاسبين
                from models.user import User
                managers = User.get_users_by_role('admin') + User.get_users_by_role('manager')
                
                for manager in managers:
                    self.create_notification(manager.id, title, message, 'warning')
            
            return True, f"تم إنشاء إشعارات لـ {len(low_stock_products)} منتج منخفض المخزون"
        
        except Exception as e:
            return False, f"خطأ في إنشاء إشعارات المخزون: {str(e)}"
    
    def create_overdue_invoice_notifications(self):
        """إنشاء إشعارات الفواتير المتأخرة"""
        try:
            conn = Notification.get_connection()
            cursor = conn.cursor()
            
            # البحث عن الفواتير المتأخرة
            cursor.execute("""
                SELECT id, invoice_number, customer_id, supplier_id, total_amount, due_date
                FROM invoices 
                WHERE status = 'pending' AND due_date < DATE('now')
            """)
            
            overdue_invoices = cursor.fetchall()
            conn.close()
            
            if not overdue_invoices:
                return True, "لا توجد فواتير متأخرة"
            
            # إنشاء إشعارات
            for invoice in overdue_invoices:
                title = "تحذير: فاتورة متأخرة"
                message = f"الفاتورة رقم {invoice['invoice_number']} متأخرة السداد. المبلغ: {invoice['total_amount']}"
                
                # إرسال للمدراء والمحاسبين
                from models.user import User
                users = User.get_users_by_role('admin') + User.get_users_by_role('manager') + User.get_users_by_role('accountant')
                
                for user in users:
                    self.create_notification(user.id, title, message, 'warning')
            
            return True, f"تم إنشاء إشعارات لـ {len(overdue_invoices)} فاتورة متأخرة"
        
        except Exception as e:
            return False, f"خطأ في إنشاء إشعارات الفواتير المتأخرة: {str(e)}"
    
    def cleanup_old_notifications(self, days=30):
        """تنظيف الإشعارات القديمة"""
        try:
            conn = Notification.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                DELETE FROM notifications 
                WHERE created_at < datetime('now', '-{} days')
            """.format(days))
            
            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()
            
            return True, f"تم حذف {deleted_count} إشعار قديم"
        
        except Exception as e:
            return False, f"خطأ في تنظيف الإشعارات: {str(e)}"
    
    def get_notification_statistics(self):
        """الحصول على إحصائيات الإشعارات"""
        try:
            conn = Notification.get_connection()
            cursor = conn.cursor()
            
            # إجمالي الإشعارات
            cursor.execute("SELECT COUNT(*) as total FROM notifications")
            total = cursor.fetchone()['total']
            
            # الإشعارات غير المقروءة
            cursor.execute("SELECT COUNT(*) as unread FROM notifications WHERE is_read = 0")
            unread = cursor.fetchone()['unread']
            
            # الإشعارات اليوم
            cursor.execute("SELECT COUNT(*) as today FROM notifications WHERE DATE(created_at) = DATE('now')")
            today = cursor.fetchone()['today']
            
            # الإشعارات حسب النوع
            cursor.execute("""
                SELECT notification_type, COUNT(*) as count 
                FROM notifications 
                GROUP BY notification_type
            """)
            by_type = {row['notification_type']: row['count'] for row in cursor.fetchall()}
            
            conn.close()
            
            return {
                'total': total,
                'unread': unread,
                'today': today,
                'by_type': by_type
            }
        
        except Exception as e:
            print(f"Error getting notification statistics: {e}")
            return {}

# إنشاء مثيل واحد من تحكم الإشعارات
notification_controller = NotificationController()
