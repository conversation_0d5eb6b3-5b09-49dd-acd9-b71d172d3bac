"""
نموذج المستخدمين
"""
import bcrypt
from datetime import datetime
from models.base_model import BaseModel

class User(BaseModel):
    """نموذج المستخدم"""
    
    table_name = "users"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.username = kwargs.get('username', '')
        self.password_hash = kwargs.get('password_hash', '')
        self.full_name = kwargs.get('full_name', '')
        self.email = kwargs.get('email', '')
        self.role = kwargs.get('role', 'user')
        self.permissions = kwargs.get('permissions', '')
        self.branch_id = kwargs.get('branch_id')
        self.is_active = kwargs.get('is_active', True)
        self.last_login = kwargs.get('last_login')
    
    def set_password(self, password):
        """تعيين كلمة المرور مع التشفير"""
        if password:
            salt = bcrypt.gensalt()
            self.password_hash = bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def check_password(self, password):
        """التحقق من كلمة المرور"""
        if not password or not self.password_hash:
            return False
        
        try:
            return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))
        except:
            return False
    
    def update_last_login(self):
        """تحديث وقت آخر تسجيل دخول"""
        self.last_login = datetime.now()
        
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute(
            "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?",
            (self.id,)
        )
        conn.commit()
        conn.close()
    
    def has_permission(self, permission):
        """فحص ما إذا كان المستخدم لديه صلاحية معينة"""
        if self.role == 'admin':
            return True
        
        if not self.permissions:
            return False
        
        permissions_list = self.permissions.split(',')
        return permission in permissions_list or 'all' in permissions_list
    
    def get_permissions_list(self):
        """الحصول على قائمة الصلاحيات"""
        if not self.permissions:
            return []
        
        return [p.strip() for p in self.permissions.split(',')]
    
    def set_permissions(self, permissions_list):
        """تعيين قائمة الصلاحيات"""
        if isinstance(permissions_list, list):
            self.permissions = ','.join(permissions_list)
        else:
            self.permissions = str(permissions_list)
    
    @classmethod
    def authenticate(cls, username, password):
        """مصادقة المستخدم"""
        if not username or not password:
            return None
        
        conn = cls.get_connection()
        cursor = conn.cursor()
        cursor.execute(
            "SELECT * FROM users WHERE username = ? AND is_active = 1",
            (username,)
        )
        result = cursor.fetchone()
        conn.close()
        
        if result:
            user = cls(**dict(result))
            if user.check_password(password):
                user.update_last_login()
                return user
        
        return None
    
    @classmethod
    def find_by_username(cls, username):
        """البحث عن مستخدم بواسطة اسم المستخدم"""
        conn = cls.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return cls(**dict(result))
        return None
    
    @classmethod
    def find_by_email(cls, email):
        """البحث عن مستخدم بواسطة البريد الإلكتروني"""
        conn = cls.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM users WHERE email = ?", (email,))
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return cls(**dict(result))
        return None
    
    @classmethod
    def get_active_users(cls):
        """الحصول على المستخدمين النشطين"""
        return cls.find_all("is_active = 1", order_by="full_name")
    
    @classmethod
    def get_users_by_role(cls, role):
        """الحصول على المستخدمين حسب الدور"""
        return cls.find_all("role = ? AND is_active = 1", [role], order_by="full_name")
    
    @classmethod
    def get_users_by_branch(cls, branch_id):
        """الحصول على المستخدمين حسب الفرع"""
        return cls.find_all("branch_id = ? AND is_active = 1", [branch_id], order_by="full_name")
    
    def get_branch(self):
        """الحصول على معلومات الفرع"""
        if not self.branch_id:
            return None
        
        from models.branch import Branch
        return Branch.find_by_id(self.branch_id)
    
    def validate(self):
        """التحقق من صحة بيانات المستخدم"""
        errors = []
        
        # التحقق من اسم المستخدم
        if not self.username or len(self.username.strip()) < 3:
            errors.append("اسم المستخدم يجب أن يكون 3 أحرف على الأقل")
        
        # التحقق من تفرد اسم المستخدم
        existing_user = self.find_by_username(self.username)
        if existing_user and existing_user.id != self.id:
            errors.append("اسم المستخدم موجود مسبقاً")
        
        # التحقق من الاسم الكامل
        if not self.full_name or len(self.full_name.strip()) < 2:
            errors.append("الاسم الكامل مطلوب")
        
        # التحقق من البريد الإلكتروني
        if self.email:
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, self.email):
                errors.append("البريد الإلكتروني غير صحيح")
            
            # التحقق من تفرد البريد الإلكتروني
            existing_user = self.find_by_email(self.email)
            if existing_user and existing_user.id != self.id:
                errors.append("البريد الإلكتروني موجود مسبقاً")
        
        # التحقق من الدور
        valid_roles = ['admin', 'manager', 'user', 'accountant', 'sales_person']
        if self.role not in valid_roles:
            errors.append("دور المستخدم غير صحيح")
        
        return len(errors) == 0, errors
    
    def to_dict(self):
        """تحويل إلى قاموس مع إخفاء كلمة المرور"""
        data = super().to_dict()
        if 'password_hash' in data:
            del data['password_hash']
        return data
    
    def get_role_display(self):
        """الحصول على اسم الدور للعرض"""
        role_names = {
            'admin': 'مدير النظام',
            'manager': 'مدير فرع',
            'user': 'مستخدم',
            'accountant': 'محاسب',
            'sales_person': 'مندوب مبيعات'
        }
        return role_names.get(self.role, self.role)
    
    def is_admin(self):
        """فحص ما إذا كان المستخدم مدير"""
        return self.role == 'admin'
    
    def is_manager(self):
        """فحص ما إذا كان المستخدم مدير فرع"""
        return self.role in ['admin', 'manager']
    
    def can_access_module(self, module_name):
        """فحص ما إذا كان المستخدم يمكنه الوصول لوحدة معينة"""
        if self.role == 'admin':
            return True
        
        # تحديد الصلاحيات المطلوبة لكل وحدة
        module_permissions = {
            'dashboard': ['dashboard'],
            'purchasing': ['purchasing', 'all'],
            'suppliers': ['suppliers', 'purchasing', 'all'],
            'customers': ['customers', 'sales', 'all'],
            'sales': ['sales', 'all'],
            'inventory': ['inventory', 'all'],
            'accounting': ['accounting', 'all'],
            'reports': ['reports', 'accounting', 'all'],
            'invoices': ['invoices', 'sales', 'purchasing', 'all'],
            'employees': ['employees', 'hr', 'all'],
            'users': ['users', 'admin', 'all'],
            'notifications': ['notifications', 'all'],
            'audit_log': ['audit_log', 'admin', 'all'],
            'backup': ['backup', 'admin', 'all'],
            'settings': ['settings', 'admin', 'all'],
            'branches': ['branches', 'admin', 'all']
        }
        
        required_permissions = module_permissions.get(module_name, [])
        user_permissions = self.get_permissions_list()
        
        return any(perm in user_permissions for perm in required_permissions)
    
    def __str__(self):
        return f"{self.full_name} ({self.username})"
