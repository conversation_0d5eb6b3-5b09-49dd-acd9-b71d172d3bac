#!/usr/bin/env python3
"""
تشغيل مبسط وآمن للنظام
"""
import sys
import os

def main():
    print("🚀 نظام إدارة الأعمال المتكامل")
    print("="*40)
    
    try:
        # إضافة مسار المشروع
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # فحص المكتبات الأساسية
        print("🔍 فحص المتطلبات...")
        
        try:
            import customtkinter as ctk
            print("✅ CustomTkinter")
        except ImportError:
            print("❌ CustomTkinter غير مثبت")
            print("تثبيت: pip install customtkinter")
            return
        
        try:
            import matplotlib
            print("✅ Matplotlib")
        except ImportError:
            print("⚠️  Matplotlib غير مثبت (اختياري)")
        
        try:
            import pandas
            print("✅ Pandas")
        except ImportError:
            print("⚠️  Pandas غير مثبت (اختياري)")
        
        try:
            import bcrypt
            print("✅ Bcrypt")
        except ImportError:
            print("❌ Bcrypt غير مثبت")
            print("تثبيت: pip install bcrypt")
            return
        
        # إعداد المجلدات
        print("📁 إعداد المجلدات...")
        directories = ["database", "backups", "logs", "assets"]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        print("✅ تم إعداد المجلدات")
        
        # إعداد قاعدة البيانات
        print("🗄️  إعداد قاعدة البيانات...")
        from config.database import db_manager
        print("✅ قاعدة البيانات جاهزة")
        
        # فحص المستخدم الافتراضي
        from models.user import User
        admin_user = User.find_by_username("admin")
        if admin_user:
            print("✅ المستخدم الافتراضي موجود")
        else:
            print("❌ المستخدم الافتراضي غير موجود")
        
        print("\n" + "="*40)
        print("🔐 معلومات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("="*40)
        
        print("\n🚀 تشغيل النظام...")
        
        # إعداد المظهر
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # تشغيل شاشة تسجيل الدخول
        from views.login_view import LoginView
        
        # إنشاء النافذة الرئيسية المخفية
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        
        # فتح شاشة تسجيل الدخول
        login_window = LoginView()
        login_window.mainloop()
        
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
