"""
الواجهة الأساسية لجميع النوافذ
"""
import customtkinter as ctk
from tkinter import messagebox
import tkinter as tk
from config.languages import get_text, get_current_language
from config.settings import COLORS, FONTS
from utils.rtl_support import RTLSupport, RTLFrame, RTLLabel, RTLButton
from controllers.auth_controller import auth_controller

class BaseView(ctk.CTkToplevel):
    """الواجهة الأساسية لجميع النوافذ"""
    
    def __init__(self, parent=None, title="", width=800, height=600):
        super().__init__(parent)
        
        self.parent = parent
        self.current_language = get_current_language()
        
        # إعداد النافذة
        self.setup_window(title, width, height)
        
        # إعداد المتغيرات
        self.setup_variables()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تطبيق الترجمة
        self.apply_translations()
        
        # ربط الأحداث
        self.bind_events()
    
    def setup_window(self, title, width, height):
        """إعداد النافذة الأساسية"""
        self.title(title or get_text('app_title', self.current_language))
        self.geometry(f"{width}x{height}")
        
        # توسيط النافذة
        self.center_window(width, height)
        
        # إعداد الألوان والمظهر
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # منع تغيير حجم النافذة (يمكن تخصيصه في النوافذ الفرعية)
        self.resizable(True, True)
        
        # إعداد الأيقونة (إذا كانت متوفرة)
        try:
            self.iconbitmap("assets/icons/app_icon.ico")
        except:
            pass
    
    def center_window(self, width, height):
        """توسيط النافذة على الشاشة"""
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_variables(self):
        """إعداد المتغيرات"""
        self.widgets = {}
        self.data = {}
        self.is_modified = False
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة - يجب تخصيصها في النوافذ الفرعية"""
        pass
    
    def apply_translations(self):
        """تطبيق الترجمات - يجب تخصيصها في النوافذ الفرعية"""
        pass
    
    def bind_events(self):
        """ربط الأحداث"""
        # حدث إغلاق النافذة
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # حدث تغيير حجم النافذة
        self.bind("<Configure>", self.on_window_configure)
        
        # اختصارات لوحة المفاتيح
        self.bind_all("<Control-s>", lambda e: self.save_data())
        self.bind_all("<Control-q>", lambda e: self.on_closing())
        self.bind_all("<Escape>", lambda e: self.on_closing())
    
    def on_closing(self):
        """حدث إغلاق النافذة"""
        if self.is_modified:
            result = messagebox.askyesnocancel(
                get_text('warning', self.current_language),
                get_text('unsaved_changes', self.current_language) or "هناك تغييرات غير محفوظة. هل تريد الحفظ؟"
            )
            
            if result is True:  # نعم - احفظ
                if self.save_data():
                    self.destroy()
            elif result is False:  # لا - لا تحفظ
                self.destroy()
            # إلغاء - لا تفعل شيئاً
        else:
            self.destroy()
    
    def on_window_configure(self, event):
        """حدث تغيير حجم النافذة"""
        if event.widget == self:
            # تحديث نشاط المستخدم
            auth_controller.update_activity()
    
    def show_message(self, message, title=None, message_type="info"):
        """عرض رسالة للمستخدم"""
        title = title or get_text('info', self.current_language)
        
        if message_type == "info":
            messagebox.showinfo(title, message)
        elif message_type == "warning":
            messagebox.showwarning(title, message)
        elif message_type == "error":
            messagebox.showerror(title, message)
        elif message_type == "success":
            messagebox.showinfo(title, message)
    
    def show_confirmation(self, message, title=None):
        """عرض رسالة تأكيد"""
        title = title or get_text('confirm', self.current_language) or "تأكيد"
        return messagebox.askyesno(title, message)
    
    def create_header_frame(self, parent, title):
        """إنشاء إطار العنوان"""
        header_frame = RTLFrame(parent, language=self.current_language)
        header_frame.pack(fill="x", padx=10, pady=(10, 5))
        
        title_label = RTLLabel(
            header_frame,
            text=title,
            language=self.current_language,
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=10)
        
        return header_frame
    
    def create_button_frame(self, parent):
        """إنشاء إطار الأزرار"""
        button_frame = RTLFrame(parent, language=self.current_language)
        button_frame.pack(fill="x", padx=10, pady=10)
        
        return button_frame
    
    def create_form_field(self, parent, label_text, field_type="entry", **kwargs):
        """إنشاء حقل نموذج"""
        field_frame = RTLFrame(parent, language=self.current_language)
        field_frame.pack(fill="x", padx=10, pady=5)
        
        # التسمية
        label = RTLLabel(
            field_frame,
            text=label_text,
            language=self.current_language,
            width=150
        )
        
        if self.current_language == "ar":
            label.pack(side="right", padx=(0, 10))
        else:
            label.pack(side="left", padx=(0, 10))
        
        # الحقل
        if field_type == "entry":
            from utils.rtl_support import RTLEntry
            field = RTLEntry(field_frame, language=self.current_language, **kwargs)
        elif field_type == "text":
            from utils.rtl_support import RTLTextbox
            field = RTLTextbox(field_frame, language=self.current_language, height=100, **kwargs)
        elif field_type == "combobox":
            from utils.rtl_support import RTLComboBox
            field = RTLComboBox(field_frame, language=self.current_language, **kwargs)
        elif field_type == "checkbox":
            field = ctk.CTkCheckBox(field_frame, text="", **kwargs)
        else:
            from utils.rtl_support import RTLEntry
            field = RTLEntry(field_frame, language=self.current_language, **kwargs)
        
        field.pack(fill="x", expand=True)
        
        return field_frame, label, field
    
    def create_data_table(self, parent, columns, data=None):
        """إنشاء جدول البيانات"""
        from tkinter import ttk
        from utils.rtl_support import create_rtl_treeview
        
        # إطار الجدول
        table_frame = RTLFrame(parent, language=self.current_language)
        table_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # إنشاء Treeview
        tree = create_rtl_treeview(table_frame, columns, self.current_language)
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient="horizontal", command=tree.xview)
        tree.configure(xscrollcommand=h_scrollbar.set)
        
        # تخطيط الجدول
        tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # تكوين الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # إضافة البيانات إذا كانت متوفرة
        if data:
            self.populate_table(tree, data)
        
        return tree
    
    def populate_table(self, tree, data):
        """ملء الجدول بالبيانات"""
        # مسح البيانات الموجودة
        for item in tree.get_children():
            tree.delete(item)
        
        # إضافة البيانات الجديدة
        for row in data:
            if isinstance(row, dict):
                values = list(row.values())
            else:
                values = row
            
            # تطبيق RTL على النصوص العربية
            if self.current_language == "ar":
                formatted_values = []
                for value in values:
                    if isinstance(value, str) and RTLSupport.is_arabic_text(value):
                        formatted_values.append(RTLSupport.reshape_arabic_text(value))
                    else:
                        formatted_values.append(str(value) if value is not None else "")
                values = formatted_values
            
            tree.insert("", "end", values=values)
    
    def save_data(self):
        """حفظ البيانات - يجب تخصيصها في النوافذ الفرعية"""
        return True
    
    def load_data(self):
        """تحميل البيانات - يجب تخصيصها في النوافذ الفرعية"""
        pass
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()
    
    def clear_form(self):
        """مسح النموذج"""
        for widget_name, widget in self.widgets.items():
            if hasattr(widget, 'delete'):
                if isinstance(widget, ctk.CTkEntry):
                    widget.delete(0, 'end')
                elif isinstance(widget, ctk.CTkTextbox):
                    widget.delete("1.0", "end")
            elif hasattr(widget, 'set'):
                if isinstance(widget, ctk.CTkComboBox):
                    widget.set("")
                elif isinstance(widget, ctk.CTkCheckBox):
                    widget.deselect()
    
    def validate_form(self):
        """التحقق من صحة النموذج - يجب تخصيصها في النوافذ الفرعية"""
        return True, []
    
    def set_modified(self, modified=True):
        """تعيين حالة التعديل"""
        self.is_modified = modified
        
        # تحديث عنوان النافذة
        current_title = self.title()
        if modified and not current_title.endswith(" *"):
            self.title(current_title + " *")
        elif not modified and current_title.endswith(" *"):
            self.title(current_title[:-2])
    
    def check_permission(self, permission):
        """فحص الصلاحية"""
        if not auth_controller.check_permission(permission):
            self.show_message(
                get_text('access_denied', self.current_language),
                get_text('error', self.current_language),
                "error"
            )
            return False
        return True
    
    def get_current_user(self):
        """الحصول على المستخدم الحالي"""
        return auth_controller.current_user
    
    def change_language(self, language):
        """تغيير اللغة"""
        from config.languages import set_language
        
        if set_language(language):
            self.current_language = language
            self.apply_translations()
            
            # إعادة إنشاء الواجهة إذا لزم الأمر
            self.refresh_ui()
    
    def refresh_ui(self):
        """تحديث الواجهة"""
        # يمكن تخصيص هذه الدالة في النوافذ الفرعية
        pass
    
    def export_data(self, data, filename, format_type="xlsx"):
        """تصدير البيانات"""
        try:
            if format_type == "xlsx":
                import pandas as pd
                df = pd.DataFrame(data)
                df.to_excel(filename, index=False)
            elif format_type == "csv":
                import pandas as pd
                df = pd.DataFrame(data)
                df.to_csv(filename, index=False, encoding='utf-8-sig')
            
            self.show_message(
                get_text('export_successful', self.current_language) or "تم التصدير بنجاح",
                get_text('success', self.current_language),
                "success"
            )
            return True
        
        except Exception as e:
            self.show_message(
                f"فشل في التصدير: {str(e)}",
                get_text('error', self.current_language),
                "error"
            )
            return False
    
    def print_data(self, data, title=""):
        """طباعة البيانات"""
        try:
            # يمكن تطوير هذه الدالة لاحقاً لدعم الطباعة
            self.show_message(
                "ميزة الطباعة قيد التطوير",
                get_text('info', self.current_language),
                "info"
            )
        except Exception as e:
            self.show_message(
                f"فشل في الطباعة: {str(e)}",
                get_text('error', self.current_language),
                "error"
            )

class BaseMainView(ctk.CTk):
    """الواجهة الرئيسية الأساسية"""
    
    def __init__(self):
        super().__init__()
        
        self.current_language = get_current_language()
        
        # إعداد النافذة الرئيسية
        self.setup_main_window()
        
        # إعداد المتغيرات
        self.setup_variables()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تطبيق الترجمة
        self.apply_translations()
        
        # ربط الأحداث
        self.bind_events()
    
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        from config.settings import WINDOW_DEFAULT_WIDTH, WINDOW_DEFAULT_HEIGHT, WINDOW_MIN_WIDTH, WINDOW_MIN_HEIGHT
        
        self.title(get_text('app_title', self.current_language))
        self.geometry(f"{WINDOW_DEFAULT_WIDTH}x{WINDOW_DEFAULT_HEIGHT}")
        self.minsize(WINDOW_MIN_WIDTH, WINDOW_MIN_HEIGHT)
        
        # توسيط النافذة
        self.center_window(WINDOW_DEFAULT_WIDTH, WINDOW_DEFAULT_HEIGHT)
        
        # إعداد المظهر
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # تعيين الأيقونة
        try:
            self.iconbitmap("assets/icons/app_icon.ico")
        except:
            pass
    
    def center_window(self, width, height):
        """توسيط النافذة على الشاشة"""
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_variables(self):
        """إعداد المتغيرات"""
        self.widgets = {}
        self.current_view = None
        self.views = {}
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة الرئيسية"""
        pass
    
    def apply_translations(self):
        """تطبيق الترجمات"""
        pass
    
    def bind_events(self):
        """ربط الأحداث"""
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def on_closing(self):
        """حدث إغلاق التطبيق"""
        if auth_controller.current_user:
            auth_controller.logout()
        
        self.quit()
        self.destroy()
    
    def show_view(self, view_name, *args, **kwargs):
        """عرض نافذة معينة"""
        # يجب تخصيص هذه الدالة في النوافذ الفرعية
        pass
