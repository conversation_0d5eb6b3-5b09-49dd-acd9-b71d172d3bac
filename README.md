# نظام إدارة الأعمال المتكامل

نظام ERP شامل مطور بلغة Python باستخدام CustomTkinter مع دعم كامل للغة العربية والإنجليزية.

## المميزات

### الوحدات الرئيسية
1. **إدارة المشتريات** - إدارة عمليات الشراء والموردين
2. **إدارة الموردين** - قاعدة بيانات شاملة للموردين
3. **إدارة العملاء** - إدارة بيانات العملاء والعلاقات
4. **إدارة المبيعات** - نظام مبيعات متكامل
5. **إدارة المخزون** - تتبع المنتجات والمخزون
6. **إدارة الحسابات العامة** - نظام محاسبي شامل
7. **التقارير المالية** - تقارير الأرباح والخسائر والميزانية
8. **إدارة الفواتير** - إنشاء وإدارة وأرشفة الفواتير
9. **إدارة الموظفين** - قاعدة بيانات الموظفين
10. **دعم تعدد المستخدمين** - نظام صلاحيات متقدم
11. **شاشة دخول آمنة** - مصادقة وحماية
12. **نظام إشعارات** - تنبيهات وإشعارات
13. **سجل النشاطات** - تتبع جميع العمليات
14. **نسخ احتياطي تلقائي** - حماية البيانات
15. **إعدادات عامة** - تخصيص النظام
16. **دعم تعدد الفروع** - إدارة فروع متعددة
17. **لوحة تحكم** - مؤشرات ورسوم بيانية

### المميزات التقنية
- **دعم ثنائي اللغة**: العربية والإنجليزية مع RTL
- **واجهة احترافية**: تصميم عصري وجذاب
- **تكيف مع الشاشة**: يتكيف مع أحجام الشاشات المختلفة
- **قاعدة بيانات SQLite**: سريعة وموثوقة
- **نظام أمان متقدم**: تشفير كلمات المرور وصلاحيات
- **رسوم بيانية**: مخططات تفاعلية للبيانات
- **تصدير البيانات**: Excel, CSV, PDF
- **نسخ احتياطي**: تلقائي ويدوي

## متطلبات النظام

### متطلبات Python
- Python 3.8 أو أحدث
- نظام التشغيل: Windows, macOS, Linux

### المكتبات المطلوبة
```
customtkinter==5.2.0
Pillow==10.0.0
matplotlib==3.7.2
pandas==2.0.3
openpyxl==3.1.2
reportlab==4.0.4
python-bidi==0.4.2
arabic-reshaper==3.0.0
bcrypt==4.0.1
```

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd erp_system
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل النظام
```bash
python main.py
```

## بيانات الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## هيكل المشروع

```
erp_system/
├── main.py                    # نقطة البداية
├── requirements.txt           # المتطلبات
├── config/                    # الإعدادات
│   ├── database.py           # قاعدة البيانات
│   ├── settings.py           # الإعدادات العامة
│   └── languages.py          # الترجمات
├── models/                    # نماذج البيانات
│   ├── base_model.py         # النموذج الأساسي
│   ├── user.py              # المستخدمين
│   └── audit_log.py         # سجل النشاطات
├── views/                     # واجهات المستخدم
│   ├── base_view.py         # الواجهة الأساسية
│   ├── login_view.py        # شاشة الدخول
│   └── dashboard_view.py    # لوحة التحكم
├── controllers/               # المتحكمات
│   └── auth_controller.py   # التحكم في المصادقة
├── utils/                     # أدوات مساعدة
│   └── rtl_support.py       # دعم النص العربي
├── database/                  # قاعدة البيانات
├── backups/                   # النسخ الاحتياطية
├── logs/                      # ملفات السجلات
└── assets/                    # الموارد
    ├── icons/                # الأيقونات
    ├── images/               # الصور
    └── fonts/                # الخطوط
```

## الاستخدام

### تسجيل الدخول
1. شغل التطبيق
2. أدخل بيانات الدخول الافتراضية
3. اختر اللغة المفضلة (عربي/إنجليزي)

### لوحة التحكم
- عرض الإحصائيات الرئيسية
- رسوم بيانية للمبيعات والأرباح
- النشاطات الأخيرة
- الوصول السريع للوحدات

### إدارة البيانات
- استخدم القائمة الجانبية للتنقل بين الوحدات
- كل وحدة تحتوي على إمكانيات CRUD كاملة
- البحث والتصفية المتقدمة
- تصدير البيانات بصيغ متعددة

### النسخ الاحتياطي
- نسخ احتياطي تلقائي كل 24 ساعة
- إمكانية إنشاء نسخة احتياطية يدوية
- استعادة البيانات من النسخ الاحتياطية

## الأمان

### حماية البيانات
- تشفير كلمات المرور باستخدام bcrypt
- نظام صلاحيات متدرج
- سجل مراجعة شامل لجميع العمليات
- حماية من SQL Injection

### إدارة المستخدمين
- أدوار متعددة: مدير، مدير فرع، محاسب، مندوب مبيعات
- صلاحيات قابلة للتخصيص
- انتهاء الجلسة التلقائي
- تتبع محاولات الدخول الفاشلة

## التخصيص

### إضافة لغات جديدة
1. أضف الترجمات في `config/languages.py`
2. حدث واجهات المستخدم لدعم اللغة الجديدة

### إضافة وحدات جديدة
1. أنشئ نموذج البيانات في `models/`
2. أنشئ واجهة المستخدم في `views/`
3. أضف الوحدة للقائمة الرئيسية

### تخصيص التقارير
- استخدم مكتبة matplotlib للرسوم البيانية
- استخدم reportlab لتقارير PDF
- استخدم pandas لمعالجة البيانات

## استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في المتطلبات**: تأكد من تثبيت جميع المكتبات المطلوبة
2. **مشاكل النص العربي**: تأكد من تثبيت arabic-reshaper و python-bidi
3. **مشاكل قاعدة البيانات**: تأكد من وجود صلاحيات الكتابة في مجلد database

### ملفات السجلات
- تحقق من مجلد `logs/` للحصول على تفاصيل الأخطاء
- سجل النشاطات متوفر في قاعدة البيانات

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

للحصول على الدعم:
- أنشئ Issue في GitHub
- راسلنا على البريد الإلكتروني
- راجع الوثائق في مجلد docs/

## خارطة الطريق

### الإصدار القادم (v1.1)
- [ ] وحدة إدارة المشتريات
- [ ] وحدة إدارة الموردين
- [ ] وحدة إدارة العملاء
- [ ] نظام التقارير المتقدم

### الإصدار المستقبلي (v2.0)
- [ ] واجهة ويب
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة خارجية
- [ ] ذكاء اصطناعي للتنبؤات

---

**تم تطوير هذا النظام بعناية لتلبية احتياجات الشركات الصغيرة والمتوسطة في المنطقة العربية.**
