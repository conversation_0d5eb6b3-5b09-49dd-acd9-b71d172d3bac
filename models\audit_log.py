"""
نموذج سجل النشاطات (Audit Log)
"""
from datetime import datetime
from models.base_model import BaseModel

class AuditLog(BaseModel):
    """نموذج سجل النشاطات"""

    table_name = "audit_log"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_id = kwargs.get('user_id')
        self.action = kwargs.get('action', '')
        self.table_name = kwargs.get('table_name', '')
        self.record_id = kwargs.get('record_id')
        self.old_values = kwargs.get('old_values')
        self.new_values = kwargs.get('new_values')
        self.ip_address = kwargs.get('ip_address')
        self.user_agent = kwargs.get('user_agent')

    def get_db_fields(self):
        """إرجاع قائمة بحقول قاعدة البيانات"""
        return [
            'id', 'user_id', 'action', 'table_name', 'record_id',
            'old_values', 'new_values', 'ip_address', 'user_agent',
            'created_at'
        ]

    @classmethod
    def log_activity(cls, user_id, action, table_name=None, record_id=None,
                    old_values=None, new_values=None, ip_address=None, user_agent=None):
        """تسجيل نشاط جديد"""
        audit_log = cls(
            user_id=user_id,
            action=action,
            table_name=table_name,
            record_id=record_id,
            old_values=old_values,
            new_values=new_values,
            ip_address=ip_address,
            user_agent=user_agent
        )
        return audit_log.create()

    @classmethod
    def get_user_activities(cls, user_id, limit=50):
        """الحصول على نشاطات مستخدم معين"""
        return cls.find_all(
            "user_id = ?",
            [user_id],
            order_by="created_at DESC",
            limit=limit
        )

    @classmethod
    def get_table_activities(cls, table_name, record_id=None, limit=50):
        """الحصول على نشاطات جدول معين"""
        if record_id:
            return cls.find_all(
                "table_name = ? AND record_id = ?",
                [table_name, record_id],
                order_by="created_at DESC",
                limit=limit
            )
        else:
            return cls.find_all(
                "table_name = ?",
                [table_name],
                order_by="created_at DESC",
                limit=limit
            )

    @classmethod
    def get_recent_activities(cls, limit=100):
        """الحصول على النشاطات الأخيرة"""
        return cls.find_all(
            order_by="created_at DESC",
            limit=limit
        )

    @classmethod
    def get_activities_by_date_range(cls, start_date, end_date, limit=1000):
        """الحصول على النشاطات في فترة زمنية محددة"""
        return cls.find_all(
            "DATE(created_at) BETWEEN ? AND ?",
            [start_date, end_date],
            order_by="created_at DESC",
            limit=limit
        )

    @classmethod
    def search_activities(cls, search_term, limit=100):
        """البحث في النشاطات"""
        return cls.find_all(
            "action LIKE ? OR table_name LIKE ? OR old_values LIKE ? OR new_values LIKE ?",
            [f"%{search_term}%", f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"],
            order_by="created_at DESC",
            limit=limit
        )

    def get_user(self):
        """الحصول على معلومات المستخدم"""
        if not self.user_id:
            return None

        from models.user import User
        return User.find_by_id(self.user_id)

    def get_action_display(self):
        """الحصول على وصف العملية للعرض"""
        action_names = {
            'CREATE': 'إنشاء',
            'UPDATE': 'تحديث',
            'DELETE': 'حذف',
            'LOGIN': 'تسجيل دخول',
            'LOGOUT': 'تسجيل خروج',
            'VIEW': 'عرض',
            'EXPORT': 'تصدير',
            'IMPORT': 'استيراد',
            'BACKUP': 'نسخ احتياطي',
            'RESTORE': 'استعادة'
        }
        return action_names.get(self.action, self.action)

    def get_table_display(self):
        """الحصول على اسم الجدول للعرض"""
        table_names = {
            'users': 'المستخدمين',
            'suppliers': 'الموردين',
            'customers': 'العملاء',
            'products': 'المنتجات',
            'invoices': 'الفواتير',
            'employees': 'الموظفين',
            'branches': 'الفروع',
            'accounts': 'الحسابات',
            'journal_entries': 'القيود المحاسبية',
            'notifications': 'الإشعارات',
            'system_settings': 'إعدادات النظام'
        }
        return table_names.get(self.table_name, self.table_name)

    def get_changes_summary(self):
        """الحصول على ملخص التغييرات"""
        if not self.old_values and not self.new_values:
            return ""

        try:
            import json

            if self.action == 'CREATE':
                if self.new_values:
                    new_data = json.loads(self.new_values)
                    return f"تم إنشاء سجل جديد: {', '.join(new_data.keys())}"
                return "تم إنشاء سجل جديد"

            elif self.action == 'DELETE':
                if self.old_values:
                    old_data = json.loads(self.old_values)
                    return f"تم حذف السجل: {old_data.get('name', old_data.get('id', ''))}"
                return "تم حذف السجل"

            elif self.action == 'UPDATE':
                changes = []
                if self.old_values and self.new_values:
                    old_data = json.loads(self.old_values)
                    new_data = json.loads(self.new_values)

                    for key in new_data:
                        if key in old_data and old_data[key] != new_data[key]:
                            changes.append(f"{key}: {old_data[key]} → {new_data[key]}")

                if changes:
                    return "تم تحديث: " + ", ".join(changes[:3])  # أول 3 تغييرات فقط
                return "تم تحديث السجل"

            return ""

        except:
            return ""

    @classmethod
    def cleanup_old_logs(cls, days_to_keep=90):
        """تنظيف السجلات القديمة"""
        try:
            conn = cls.get_connection()
            cursor = conn.cursor()
            cursor.execute(
                f"DELETE FROM {cls.table_name} WHERE created_at < datetime('now', '-{days_to_keep} days')"
            )
            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()
            return deleted_count
        except Exception as e:
            print(f"Error cleaning up audit logs: {e}")
            return 0

    @classmethod
    def get_statistics(cls):
        """الحصول على إحصائيات سجل النشاطات"""
        conn = cls.get_connection()
        cursor = conn.cursor()

        # إجمالي النشاطات
        cursor.execute(f"SELECT COUNT(*) as total FROM {cls.table_name}")
        total = cursor.fetchone()['total']

        # النشاطات اليوم
        cursor.execute(f"SELECT COUNT(*) as today FROM {cls.table_name} WHERE DATE(created_at) = DATE('now')")
        today = cursor.fetchone()['today']

        # النشاطات هذا الأسبوع
        cursor.execute(f"SELECT COUNT(*) as week FROM {cls.table_name} WHERE created_at >= datetime('now', '-7 days')")
        week = cursor.fetchone()['week']

        # النشاطات هذا الشهر
        cursor.execute(f"SELECT COUNT(*) as month FROM {cls.table_name} WHERE created_at >= datetime('now', 'start of month')")
        month = cursor.fetchone()['month']

        # أكثر العمليات تكراراً
        cursor.execute(f"""
            SELECT action, COUNT(*) as count
            FROM {cls.table_name}
            GROUP BY action
            ORDER BY count DESC
            LIMIT 5
        """)
        top_actions = cursor.fetchall()

        # أكثر المستخدمين نشاطاً
        cursor.execute(f"""
            SELECT user_id, COUNT(*) as count
            FROM {cls.table_name}
            WHERE user_id IS NOT NULL
            GROUP BY user_id
            ORDER BY count DESC
            LIMIT 5
        """)
        top_users = cursor.fetchall()

        conn.close()

        return {
            'total': total,
            'today': today,
            'week': week,
            'month': month,
            'top_actions': [dict(row) for row in top_actions],
            'top_users': [dict(row) for row in top_users]
        }

    def validate(self):
        """التحقق من صحة البيانات"""
        errors = []

        if not self.action:
            errors.append("العملية مطلوبة")

        return len(errors) == 0, errors

    def __str__(self):
        user = self.get_user()
        user_name = user.full_name if user else "مجهول"
        return f"{user_name} - {self.get_action_display()} - {self.get_table_display()}"
