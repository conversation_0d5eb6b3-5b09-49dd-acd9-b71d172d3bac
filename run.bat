@echo off
chcp 65001 >nul
echo ================================================
echo نظام إدارة الأعمال المتكامل
echo ================================================
echo.

REM فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    pause
    exit /b 1
)

REM تثبيت المتطلبات
echo جاري تثبيت المتطلبات...
python -m pip install -r requirements.txt

REM تشغيل النظام
echo.
echo جاري تشغيل النظام...
echo بيانات الدخول الافتراضية:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo ------------------------------
echo.

python launch_erp.py

pause
