#!/bin/bash

# تعيين ترميز UTF-8
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

echo "================================================"
echo "نظام إدارة الأعمال المتكامل"
echo "Integrated Business Management System"
echo "================================================"
echo

# فحص وجود Python
if ! command -v python3 &> /dev/null; then
    echo "❌ خطأ: Python3 غير مثبت"
    echo "يرجى تثبيت Python 3.8 أو أحدث"
    exit 1
fi

# فحص إصدار Python
python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ خطأ: يتطلب Python 3.8 أو أحدث"
    echo "الإصدار الحالي: $python_version"
    exit 1
fi

echo "✅ Python $python_version - متوافق"

# تثبيت المتطلبات
echo "📦 تثبيت المتطلبات..."
python3 -m pip install -r requirements.txt

echo
echo "🚀 تشغيل النظام..."
echo "بيانات الدخول الافتراضية:"
echo "اسم المستخدم: admin"
echo "كلمة المرور: admin123"
echo "------------------------------"
echo

# تشغيل النظام
python3 start_erp.py

echo
echo "👋 شكراً لاستخدام نظام إدارة الأعمال المتكامل"
