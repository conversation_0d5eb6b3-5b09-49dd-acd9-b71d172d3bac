"""
لوحة التحكم الرئيسية
"""
import customtkinter as ctk
from tkinter import messagebox
import tkinter as tk
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.dates as mdates
from config.languages import get_text, get_current_language
from utils.rtl_support import RTLFrame, RTLLabel, RTLButton
from views.base_view import BaseMainView
from controllers.auth_controller import auth_controller

class DashboardView(BaseMainView):
    """لوحة التحكم الرئيسية"""
    
    def __init__(self):
        super().__init__()
        
        # تحميل البيانات
        self.load_dashboard_data()
        
        # تحديث البيانات كل 5 دقائق
        self.schedule_data_refresh()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إنشاء القائمة الجانبية
        self.create_sidebar()
        
        # إنشاء المحتوى الرئيسي
        self.create_main_content()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
    
    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        # إطار القائمة الجانبية
        self.sidebar_frame = RTLFrame(self, language=self.current_language, width=250)
        self.sidebar_frame.pack(side="right" if self.current_language == "ar" else "left", fill="y", padx=(10, 0), pady=10)
        self.sidebar_frame.pack_propagate(False)
        
        # عنوان التطبيق
        app_title = RTLLabel(
            self.sidebar_frame,
            text=get_text('app_title', self.current_language),
            language=self.current_language,
            font=ctk.CTkFont(size=18, weight="bold")
        )
        app_title.pack(pady=(20, 30))
        
        # معلومات المستخدم
        self.create_user_info_section()
        
        # قائمة الوحدات
        self.create_modules_menu()
        
        # أزرار النظام
        self.create_system_buttons()
    
    def create_user_info_section(self):
        """إنشاء قسم معلومات المستخدم"""
        user_frame = RTLFrame(self.sidebar_frame, language=self.current_language)
        user_frame.pack(fill="x", padx=10, pady=(0, 20))
        
        # صورة المستخدم (رمز افتراضي)
        user_icon = RTLLabel(
            user_frame,
            text="👤",
            language=self.current_language,
            font=ctk.CTkFont(size=30)
        )
        user_icon.pack(pady=(0, 5))
        
        # اسم المستخدم
        user = auth_controller.current_user
        if user:
            user_name = RTLLabel(
                user_frame,
                text=user.full_name,
                language=self.current_language,
                font=ctk.CTkFont(size=14, weight="bold")
            )
            user_name.pack()
            
            # دور المستخدم
            user_role = RTLLabel(
                user_frame,
                text=user.get_role_display(),
                language=self.current_language,
                font=ctk.CTkFont(size=12),
                text_color="gray"
            )
            user_role.pack()
    
    def create_modules_menu(self):
        """إنشاء قائمة الوحدات"""
        modules_frame = RTLFrame(self.sidebar_frame, language=self.current_language)
        modules_frame.pack(fill="x", padx=10, pady=(0, 20))
        
        # عنوان القائمة
        menu_title = RTLLabel(
            modules_frame,
            text="الوحدات",
            language=self.current_language,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        menu_title.pack(pady=(0, 10))
        
        # قائمة الوحدات المتاحة
        modules = [
            ('dashboard', 'لوحة التحكم', '📊'),
            ('purchasing', 'المشتريات', '🛒'),
            ('suppliers', 'الموردين', '🏭'),
            ('customers', 'العملاء', '👥'),
            ('sales', 'المبيعات', '💰'),
            ('inventory', 'المخزون', '📦'),
            ('accounting', 'الحسابات العامة', '📚'),
            ('reports', 'التقارير المالية', '📈'),
            ('invoices', 'إدارة الفواتير', '🧾'),
            ('employees', 'الموظفين', '👨‍💼'),
            ('users', 'إدارة المستخدمين', '👤'),
            ('notifications', 'الإشعارات', '🔔'),
            ('audit_log', 'سجل النشاطات', '📋'),
            ('backup', 'النسخ الاحتياطي', '💾'),
            ('settings', 'الإعدادات', '⚙️'),
            ('branches', 'الفروع', '🏢')
        ]
        
        self.module_buttons = {}
        
        for module_id, module_name, icon in modules:
            # فحص الصلاحية
            if not auth_controller.can_access_module(module_id):
                continue
            
            button = RTLButton(
                modules_frame,
                text=f"{icon} {module_name}",
                language=self.current_language,
                command=lambda m=module_id: self.open_module(m),
                height=35,
                anchor="w" if self.current_language == "en" else "e",
                fg_color="transparent",
                text_color=("gray10", "gray90"),
                hover_color=("gray80", "gray20")
            )
            button.pack(fill="x", pady=2)
            
            self.module_buttons[module_id] = button
        
        # تمييز الوحدة الحالية (لوحة التحكم)
        if 'dashboard' in self.module_buttons:
            self.module_buttons['dashboard'].configure(
                fg_color=("gray75", "gray25"),
                text_color=("gray10", "white")
            )
    
    def create_system_buttons(self):
        """إنشاء أزرار النظام"""
        system_frame = RTLFrame(self.sidebar_frame, language=self.current_language)
        system_frame.pack(side="bottom", fill="x", padx=10, pady=10)
        
        # زر تسجيل الخروج
        logout_button = RTLButton(
            system_frame,
            text=f"🚪 {get_text('logout', self.current_language)}",
            language=self.current_language,
            command=self.logout,
            height=35,
            fg_color="red",
            hover_color="darkred"
        )
        logout_button.pack(fill="x", pady=(0, 5))
        
        # زر المساعدة
        help_button = RTLButton(
            system_frame,
            text=f"❓ {get_text('help', self.current_language)}",
            language=self.current_language,
            command=self.show_help,
            height=35,
            fg_color="transparent",
            text_color=("gray10", "gray90"),
            hover_color=("gray80", "gray20")
        )
        help_button.pack(fill="x")
    
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # إطار المحتوى الرئيسي
        self.main_frame = RTLFrame(self, language=self.current_language)
        self.main_frame.pack(side="left" if self.current_language == "ar" else "right", fill="both", expand=True, padx=10, pady=10)
        
        # عنوان لوحة التحكم
        dashboard_title = RTLLabel(
            self.main_frame,
            text=get_text('dashboard', self.current_language),
            language=self.current_language,
            font=ctk.CTkFont(size=24, weight="bold")
        )
        dashboard_title.pack(pady=(10, 20))
        
        # إنشاء بطاقات الإحصائيات
        self.create_stats_cards()
        
        # إنشاء الرسوم البيانية
        self.create_charts_section()
        
        # إنشاء قسم النشاطات الأخيرة
        self.create_recent_activities()
    
    def create_stats_cards(self):
        """إنشاء بطاقات الإحصائيات"""
        stats_frame = RTLFrame(self.main_frame, language=self.current_language)
        stats_frame.pack(fill="x", pady=(0, 20))
        
        # إطار البطاقات
        cards_frame = RTLFrame(stats_frame, language=self.current_language)
        cards_frame.pack(fill="x")
        
        # تكوين الشبكة
        for i in range(4):
            cards_frame.grid_columnconfigure(i, weight=1)
        
        # بطاقة إجمالي المبيعات
        self.create_stat_card(
            cards_frame, 
            "💰", 
            get_text('total_sales', self.current_language),
            f"{self.dashboard_data.get('total_sales', 0):,.2f} ريال",
            0, 0
        )
        
        # بطاقة إجمالي المشتريات
        self.create_stat_card(
            cards_frame,
            "🛒",
            get_text('total_purchases', self.current_language),
            f"{self.dashboard_data.get('total_purchases', 0):,.2f} ريال",
            0, 1
        )
        
        # بطاقة الفواتير المعلقة
        self.create_stat_card(
            cards_frame,
            "🧾",
            get_text('pending_invoices', self.current_language),
            str(self.dashboard_data.get('pending_invoices', 0)),
            0, 2
        )
        
        # بطاقة المنتجات منخفضة المخزون
        self.create_stat_card(
            cards_frame,
            "📦",
            get_text('low_stock_items', self.current_language),
            str(self.dashboard_data.get('low_stock_items', 0)),
            0, 3
        )
    
    def create_stat_card(self, parent, icon, title, value, row, col):
        """إنشاء بطاقة إحصائية"""
        card_frame = RTLFrame(parent, language=self.current_language, corner_radius=10)
        card_frame.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
        
        # الأيقونة
        icon_label = RTLLabel(
            card_frame,
            text=icon,
            language=self.current_language,
            font=ctk.CTkFont(size=30)
        )
        icon_label.pack(pady=(15, 5))
        
        # القيمة
        value_label = RTLLabel(
            card_frame,
            text=value,
            language=self.current_language,
            font=ctk.CTkFont(size=20, weight="bold")
        )
        value_label.pack(pady=(0, 5))
        
        # العنوان
        title_label = RTLLabel(
            card_frame,
            text=title,
            language=self.current_language,
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        title_label.pack(pady=(0, 15))
    
    def create_charts_section(self):
        """إنشاء قسم الرسوم البيانية"""
        charts_frame = RTLFrame(self.main_frame, language=self.current_language)
        charts_frame.pack(fill="both", expand=True, pady=(0, 20))
        
        # تكوين الشبكة
        charts_frame.grid_columnconfigure(0, weight=1)
        charts_frame.grid_columnconfigure(1, weight=1)
        charts_frame.grid_rowconfigure(0, weight=1)
        
        # رسم بياني للمبيعات الشهرية
        self.create_sales_chart(charts_frame, 0, 0)
        
        # رسم بياني للأرباح والخسائر
        self.create_profit_chart(charts_frame, 0, 1)
    
    def create_sales_chart(self, parent, row, col):
        """إنشاء رسم بياني للمبيعات"""
        chart_frame = RTLFrame(parent, language=self.current_language, corner_radius=10)
        chart_frame.grid(row=row, column=col, padx=5, pady=5, sticky="nsew")
        
        # عنوان الرسم البياني
        chart_title = RTLLabel(
            chart_frame,
            text=get_text('monthly_sales', self.current_language),
            language=self.current_language,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        chart_title.pack(pady=(10, 5))
        
        # إنشاء الرسم البياني
        fig, ax = plt.subplots(figsize=(6, 4))
        fig.patch.set_facecolor('#f0f0f0')
        
        # بيانات وهمية للمبيعات الشهرية
        months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو']
        sales = [50000, 65000, 45000, 70000, 80000, 75000]
        
        ax.plot(months, sales, marker='o', linewidth=2, markersize=6, color='#2E86AB')
        ax.fill_between(months, sales, alpha=0.3, color='#2E86AB')
        
        ax.set_title('مبيعات آخر 6 أشهر', fontsize=12, pad=20)
        ax.set_ylabel('المبلغ (ريال)', fontsize=10)
        ax.grid(True, alpha=0.3)
        
        # تنسيق المحاور
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # إضافة الرسم البياني للواجهة
        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill="both", expand=True, padx=10, pady=(0, 10))
    
    def create_profit_chart(self, parent, row, col):
        """إنشاء رسم بياني للأرباح"""
        chart_frame = RTLFrame(parent, language=self.current_language, corner_radius=10)
        chart_frame.grid(row=row, column=col, padx=5, pady=5, sticky="nsew")
        
        # عنوان الرسم البياني
        chart_title = RTLLabel(
            chart_frame,
            text=get_text('profit_loss', self.current_language),
            language=self.current_language,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        chart_title.pack(pady=(10, 5))
        
        # إنشاء الرسم البياني
        fig, ax = plt.subplots(figsize=(6, 4))
        fig.patch.set_facecolor('#f0f0f0')
        
        # بيانات وهمية للأرباح والخسائر
        categories = ['الإيرادات', 'التكاليف', 'صافي الربح']
        values = [150000, 100000, 50000]
        colors = ['#28A745', '#DC3545', '#2E86AB']
        
        bars = ax.bar(categories, values, color=colors, alpha=0.8)
        
        # إضافة قيم على الأعمدة
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1000,
                   f'{value:,.0f}', ha='center', va='bottom', fontsize=10)
        
        ax.set_title('الأرباح والخسائر - الشهر الحالي', fontsize=12, pad=20)
        ax.set_ylabel('المبلغ (ريال)', fontsize=10)
        ax.grid(True, alpha=0.3, axis='y')
        
        plt.tight_layout()
        
        # إضافة الرسم البياني للواجهة
        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill="both", expand=True, padx=10, pady=(0, 10))
    
    def create_recent_activities(self):
        """إنشاء قسم النشاطات الأخيرة"""
        activities_frame = RTLFrame(self.main_frame, language=self.current_language, corner_radius=10)
        activities_frame.pack(fill="x", pady=(0, 10))
        
        # عنوان القسم
        activities_title = RTLLabel(
            activities_frame,
            text=get_text('recent_activities', self.current_language),
            language=self.current_language,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        activities_title.pack(pady=(15, 10))
        
        # قائمة النشاطات
        activities_list = RTLFrame(activities_frame, language=self.current_language)
        activities_list.pack(fill="x", padx=15, pady=(0, 15))
        
        # نشاطات وهمية
        activities = [
            ("👤", "تم تسجيل دخول المستخدم أحمد محمد", "منذ 5 دقائق"),
            ("🧾", "تم إنشاء فاتورة مبيعات جديدة #1001", "منذ 15 دقيقة"),
            ("📦", "تحذير: منتج 'لابتوب ديل' منخفض المخزون", "منذ 30 دقيقة"),
            ("💰", "تم استلام دفعة من العميل 'شركة النور'", "منذ ساعة"),
            ("👥", "تم إضافة عميل جديد 'مؤسسة الفجر'", "منذ ساعتين")
        ]
        
        for icon, activity, time in activities:
            activity_item = RTLFrame(activities_list, language=self.current_language)
            activity_item.pack(fill="x", pady=2)
            
            # الأيقونة
            icon_label = RTLLabel(
                activity_item,
                text=icon,
                language=self.current_language,
                font=ctk.CTkFont(size=16)
            )
            
            if self.current_language == "ar":
                icon_label.pack(side="right", padx=(0, 10))
            else:
                icon_label.pack(side="left", padx=(0, 10))
            
            # الوقت
            time_label = RTLLabel(
                activity_item,
                text=time,
                language=self.current_language,
                font=ctk.CTkFont(size=10),
                text_color="gray"
            )
            
            if self.current_language == "ar":
                time_label.pack(side="left", padx=(10, 0))
            else:
                time_label.pack(side="right", padx=(10, 0))
            
            # النشاط
            activity_label = RTLLabel(
                activity_item,
                text=activity,
                language=self.current_language,
                font=ctk.CTkFont(size=12)
            )
            activity_label.pack(fill="x", expand=True)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = RTLFrame(self, language=self.current_language, height=30)
        status_frame.pack(side="bottom", fill="x", padx=10, pady=(0, 10))
        status_frame.pack_propagate(False)
        
        # معلومات الاتصال
        connection_label = RTLLabel(
            status_frame,
            text="🟢 متصل",
            language=self.current_language,
            font=ctk.CTkFont(size=10)
        )
        
        if self.current_language == "ar":
            connection_label.pack(side="right", padx=(0, 10))
        else:
            connection_label.pack(side="left", padx=(0, 10))
        
        # الوقت الحالي
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        time_label = RTLLabel(
            status_frame,
            text=current_time,
            language=self.current_language,
            font=ctk.CTkFont(size=10)
        )
        
        if self.current_language == "ar":
            time_label.pack(side="left", padx=(10, 0))
        else:
            time_label.pack(side="right", padx=(10, 0))
        
        # تحديث الوقت كل ثانية
        self.update_time(time_label)
    
    def update_time(self, time_label):
        """تحديث الوقت في شريط الحالة"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        time_label.configure(text=current_time)
        self.after(1000, lambda: self.update_time(time_label))
    
    def load_dashboard_data(self):
        """تحميل بيانات لوحة التحكم"""
        try:
            # هنا يمكن تحميل البيانات الفعلية من قاعدة البيانات
            # حالياً سنستخدم بيانات وهمية
            self.dashboard_data = {
                'total_sales': 250000.00,
                'total_purchases': 180000.00,
                'pending_invoices': 15,
                'low_stock_items': 8,
                'active_users': 5,
                'monthly_sales': [50000, 65000, 45000, 70000, 80000, 75000],
                'monthly_purchases': [35000, 45000, 30000, 50000, 55000, 52000]
            }
        except Exception as e:
            print(f"Error loading dashboard data: {e}")
            self.dashboard_data = {}
    
    def schedule_data_refresh(self):
        """جدولة تحديث البيانات"""
        # تحديث البيانات كل 5 دقائق (300000 مللي ثانية)
        self.after(300000, self.refresh_dashboard_data)
    
    def refresh_dashboard_data(self):
        """تحديث بيانات لوحة التحكم"""
        self.load_dashboard_data()
        # يمكن إضافة كود لتحديث العناصر المرئية هنا
        self.schedule_data_refresh()
    
    def open_module(self, module_id):
        """فتح وحدة معينة"""
        try:
            # إعادة تعيين ألوان جميع الأزرار
            for button in self.module_buttons.values():
                button.configure(
                    fg_color="transparent",
                    text_color=("gray10", "gray90")
                )
            
            # تمييز الزر المحدد
            if module_id in self.module_buttons:
                self.module_buttons[module_id].configure(
                    fg_color=("gray75", "gray25"),
                    text_color=("gray10", "white")
                )
            
            # فتح الوحدة المطلوبة
            if module_id == 'dashboard':
                # نحن بالفعل في لوحة التحكم
                pass
            elif module_id == 'suppliers':
                self.open_suppliers_view()
            elif module_id == 'customers':
                self.open_customers_view()
            elif module_id == 'products':
                self.open_products_view()
            elif module_id == 'invoices':
                self.open_invoices_view()
            elif module_id == 'reports':
                self.open_reports_view()
            elif module_id == 'settings':
                self.open_settings_view()
            elif module_id == 'users':
                self.open_users_view()
            else:
                # وحدة قيد التطوير
                messagebox.showinfo(
                    get_text('info', self.current_language),
                    f"وحدة {module_id} قيد التطوير"
                )
        
        except Exception as e:
            messagebox.showerror(
                get_text('error', self.current_language),
                f"خطأ في فتح الوحدة: {str(e)}"
            )
    
    def open_suppliers_view(self):
        """فتح نافذة الموردين"""
        try:
            from views.suppliers_view import SuppliersView
            suppliers_window = SuppliersView(self)
        except ImportError:
            messagebox.showinfo(
                get_text('info', self.current_language),
                "نافذة الموردين قيد التطوير"
            )
    
    def open_customers_view(self):
        """فتح نافذة العملاء"""
        try:
            from views.customers_view import CustomersView
            customers_window = CustomersView(self)
        except ImportError:
            messagebox.showinfo(
                get_text('info', self.current_language),
                "نافذة العملاء قيد التطوير"
            )
    
    def open_products_view(self):
        """فتح نافذة المنتجات"""
        try:
            from views.products_view import ProductsView
            products_window = ProductsView(self)
        except ImportError:
            messagebox.showinfo(
                get_text('info', self.current_language),
                "نافذة المنتجات قيد التطوير"
            )
    
    def open_invoices_view(self):
        """فتح نافذة الفواتير"""
        try:
            from views.invoices_view import InvoicesView
            invoices_window = InvoicesView(self)
        except ImportError:
            messagebox.showinfo(
                get_text('info', self.current_language),
                "نافذة الفواتير قيد التطوير"
            )
    
    def open_reports_view(self):
        """فتح نافذة التقارير"""
        try:
            from views.reports_view import ReportsView
            reports_window = ReportsView(self)
        except ImportError:
            messagebox.showinfo(
                get_text('info', self.current_language),
                "نافذة التقارير قيد التطوير"
            )
    
    def open_settings_view(self):
        """فتح نافذة الإعدادات"""
        try:
            from views.settings_view import SettingsView
            settings_window = SettingsView(self)
        except ImportError:
            messagebox.showinfo(
                get_text('info', self.current_language),
                "نافذة الإعدادات قيد التطوير"
            )
    
    def open_users_view(self):
        """فتح نافذة المستخدمين"""
        try:
            from views.users_view import UsersView
            users_window = UsersView(self)
        except ImportError:
            messagebox.showinfo(
                get_text('info', self.current_language),
                "نافذة المستخدمين قيد التطوير"
            )
    
    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno(
            get_text('logout', self.current_language),
            "هل أنت متأكد من تسجيل الخروج؟"
        )
        
        if result:
            # تسجيل الخروج
            auth_controller.logout()
            
            # إغلاق النافذة الحالية
            self.destroy()
    
    def show_help(self):
        """عرض المساعدة"""
        help_text = """
        مرحباً بك في نظام إدارة الأعمال المتكامل
        
        الوحدات المتاحة:
        • لوحة التحكم: عرض الإحصائيات والرسوم البيانية
        • المشتريات: إدارة عمليات الشراء
        • الموردين: إدارة بيانات الموردين
        • العملاء: إدارة بيانات العملاء
        • المبيعات: إدارة عمليات البيع
        • المخزون: إدارة المنتجات والمخزون
        • الحسابات العامة: النظام المحاسبي
        • التقارير المالية: تقارير الأرباح والخسائر
        • إدارة الفواتير: إنشاء وإدارة الفواتير
        • الموظفين: إدارة بيانات الموظفين
        • إدارة المستخدمين: إدارة مستخدمي النظام
        • الإشعارات: نظام الإشعارات
        • سجل النشاطات: تتبع جميع العمليات
        • النسخ الاحتياطي: حفظ واستعادة البيانات
        • الإعدادات: إعدادات النظام العامة
        • الفروع: إدارة فروع الشركة
        
        للمساعدة الإضافية، يرجى الاتصال بمدير النظام.
        """
        
        messagebox.showinfo(
            get_text('help', self.current_language),
            help_text
        )
    
    def apply_translations(self):
        """تطبيق الترجمات"""
        self.title(get_text('app_title', self.current_language))
        # يمكن إضافة المزيد من الترجمات هنا
