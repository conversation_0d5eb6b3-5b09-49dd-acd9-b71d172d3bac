"""
واجهة إدارة المستخدمين
"""
import customtkinter as ctk
from tkinter import messagebox
import tkinter as tk
from tkinter import ttk
from config.languages import get_text, get_current_language
from utils.rtl_support import RTLFrame, RTLLabel, RTLButton, RTLEntry, RTLComboBox
from views.base_view import BaseView
from models.user import User
from models.branch import Branch
from controllers.auth_controller import auth_controller

class UsersView(BaseView):
    """واجهة إدارة المستخدمين"""
    
    def __init__(self, parent=None):
        super().__init__(parent, "إدارة المستخدمين", 1000, 700)
        
        # فحص الصلاحية
        if not self.check_permission('users'):
            self.destroy()
            return
        
        # تحميل البيانات
        self.load_data()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إنشاء الإطار الرئيسي
        main_frame = RTLFrame(self, language=self.current_language)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # عنوان الصفحة
        self.create_header_frame(main_frame, get_text('users', self.current_language))
        
        # إنشاء شريط الأدوات
        self.create_toolbar(main_frame)
        
        # إنشاء الإطار المقسم
        paned_window = tk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill="both", expand=True, pady=10)
        
        # الجانب الأيسر - قائمة المستخدمين
        self.create_users_list(paned_window)
        
        # الجانب الأيمن - تفاصيل المستخدم
        self.create_user_details(paned_window)
    
    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = RTLFrame(parent, language=self.current_language)
        toolbar_frame.pack(fill="x", pady=(0, 10))
        
        # أزرار العمليات
        self.widgets['add_button'] = RTLButton(
            toolbar_frame,
            text=f"➕ {get_text('add_user', self.current_language)}",
            language=self.current_language,
            command=self.add_user,
            width=120
        )
        
        self.widgets['edit_button'] = RTLButton(
            toolbar_frame,
            text=f"✏️ {get_text('edit_user', self.current_language)}",
            language=self.current_language,
            command=self.edit_user,
            width=120,
            state="disabled"
        )
        
        self.widgets['delete_button'] = RTLButton(
            toolbar_frame,
            text=f"🗑️ {get_text('delete', self.current_language)}",
            language=self.current_language,
            command=self.delete_user,
            width=120,
            state="disabled",
            fg_color="red",
            hover_color="darkred"
        )
        
        self.widgets['refresh_button'] = RTLButton(
            toolbar_frame,
            text=f"🔄 {get_text('refresh', self.current_language)}",
            language=self.current_language,
            command=self.refresh_data,
            width=120
        )
        
        # ترتيب الأزرار
        if self.current_language == "ar":
            self.widgets['add_button'].pack(side="right", padx=5)
            self.widgets['edit_button'].pack(side="right", padx=5)
            self.widgets['delete_button'].pack(side="right", padx=5)
            self.widgets['refresh_button'].pack(side="right", padx=5)
        else:
            self.widgets['add_button'].pack(side="left", padx=5)
            self.widgets['edit_button'].pack(side="left", padx=5)
            self.widgets['delete_button'].pack(side="left", padx=5)
            self.widgets['refresh_button'].pack(side="left", padx=5)
        
        # حقل البحث
        search_frame = RTLFrame(toolbar_frame, language=self.current_language)
        search_frame.pack(side="left" if self.current_language == "ar" else "right", padx=10)
        
        RTLLabel(
            search_frame,
            text=get_text('search', self.current_language),
            language=self.current_language
        ).pack(side="right" if self.current_language == "ar" else "left", padx=(0, 5))
        
        self.widgets['search_entry'] = RTLEntry(
            search_frame,
            language=self.current_language,
            width=200,
            placeholder_text="البحث في المستخدمين..."
        )
        self.widgets['search_entry'].pack(side="right" if self.current_language == "ar" else "left")
        self.widgets['search_entry'].bind("<KeyRelease>", self.on_search)
    
    def create_users_list(self, parent):
        """إنشاء قائمة المستخدمين"""
        # إطار قائمة المستخدمين
        list_frame = RTLFrame(parent, language=self.current_language)
        list_frame.pack(fill="both", expand=True)
        parent.add(list_frame)
        
        # عنوان القائمة
        RTLLabel(
            list_frame,
            text=get_text('users', self.current_language),
            language=self.current_language,
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(0, 10))
        
        # إنشاء Treeview للمستخدمين
        columns = ('ID', 'الاسم الكامل', 'اسم المستخدم', 'الدور', 'الحالة', 'آخر دخول')
        self.widgets['users_tree'] = self.create_data_table(list_frame, columns)
        
        # ربط حدث التحديد
        self.widgets['users_tree'].bind('<<TreeviewSelect>>', self.on_user_select)
        self.widgets['users_tree'].bind('<Double-1>', self.edit_user)
    
    def create_user_details(self, parent):
        """إنشاء تفاصيل المستخدم"""
        # إطار التفاصيل
        details_frame = RTLFrame(parent, language=self.current_language)
        details_frame.pack(fill="both", expand=True)
        parent.add(details_frame)
        
        # عنوان التفاصيل
        RTLLabel(
            details_frame,
            text=get_text('user_details', self.current_language),
            language=self.current_language,
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(0, 20))
        
        # إطار النموذج
        form_frame = RTLFrame(details_frame, language=self.current_language)
        form_frame.pack(fill="x", padx=20)
        
        # الحقول
        self.create_form_fields(form_frame)
        
        # أزرار الحفظ والإلغاء
        self.create_form_buttons(details_frame)
    
    def create_form_fields(self, parent):
        """إنشاء حقول النموذج"""
        # اسم المستخدم
        _, _, self.widgets['username_entry'] = self.create_form_field(
            parent, get_text('username', self.current_language), "entry"
        )
        
        # كلمة المرور
        _, _, self.widgets['password_entry'] = self.create_form_field(
            parent, get_text('password', self.current_language), "entry", show="*"
        )
        
        # الاسم الكامل
        _, _, self.widgets['full_name_entry'] = self.create_form_field(
            parent, get_text('full_name', self.current_language), "entry"
        )
        
        # البريد الإلكتروني
        _, _, self.widgets['email_entry'] = self.create_form_field(
            parent, get_text('email', self.current_language), "entry"
        )
        
        # الدور
        roles = ['admin', 'manager', 'user', 'accountant', 'sales_person']
        role_names = [get_text(role, self.current_language) for role in roles]
        
        _, _, self.widgets['role_combobox'] = self.create_form_field(
            parent, get_text('user_role', self.current_language), "combobox", values=role_names
        )
        
        # الفرع
        branches = Branch.get_active_branches()
        branch_names = [branch.name for branch in branches]
        
        _, _, self.widgets['branch_combobox'] = self.create_form_field(
            parent, get_text('branch_name', self.current_language), "combobox", values=branch_names
        )
        
        # الصلاحيات
        _, _, self.widgets['permissions_entry'] = self.create_form_field(
            parent, get_text('permissions', self.current_language), "text"
        )
        
        # الحالة
        _, _, self.widgets['is_active_checkbox'] = self.create_form_field(
            parent, get_text('active', self.current_language), "checkbox"
        )
        
        # حفظ مراجع الفروع والأدوار
        self.branches = branches
        self.roles = roles
    
    def create_form_buttons(self, parent):
        """إنشاء أزرار النموذج"""
        buttons_frame = self.create_button_frame(parent)
        
        self.widgets['save_button'] = RTLButton(
            buttons_frame,
            text=get_text('save', self.current_language),
            language=self.current_language,
            command=self.save_user,
            state="disabled"
        )
        
        self.widgets['cancel_button'] = RTLButton(
            buttons_frame,
            text=get_text('cancel', self.current_language),
            language=self.current_language,
            command=self.cancel_edit,
            state="disabled"
        )
        
        if self.current_language == "ar":
            self.widgets['save_button'].pack(side="right", padx=5)
            self.widgets['cancel_button'].pack(side="right", padx=5)
        else:
            self.widgets['save_button'].pack(side="left", padx=5)
            self.widgets['cancel_button'].pack(side="left", padx=5)
    
    def load_data(self):
        """تحميل بيانات المستخدمين"""
        try:
            users = User.find_all(order_by="full_name")
            self.users_data = users
            self.display_users(users)
        except Exception as e:
            self.show_message(f"خطأ في تحميل البيانات: {str(e)}", "خطأ", "error")
    
    def display_users(self, users):
        """عرض المستخدمين في الجدول"""
        # مسح البيانات الموجودة
        for item in self.widgets['users_tree'].get_children():
            self.widgets['users_tree'].delete(item)
        
        # إضافة المستخدمين
        for user in users:
            last_login = user.last_login.strftime("%Y-%m-%d %H:%M") if user.last_login else "لم يسجل دخول"
            status = "نشط" if user.is_active else "غير نشط"
            
            self.widgets['users_tree'].insert("", "end", values=(
                user.id,
                user.full_name,
                user.username,
                user.get_role_display(),
                status,
                last_login
            ))
    
    def on_user_select(self, event):
        """حدث تحديد مستخدم"""
        selection = self.widgets['users_tree'].selection()
        if selection:
            item = self.widgets['users_tree'].item(selection[0])
            user_id = item['values'][0]
            
            # البحث عن المستخدم
            user = next((u for u in self.users_data if u.id == user_id), None)
            if user:
                self.selected_user = user
                self.display_user_details(user)
                
                # تفعيل أزرار التحرير والحذف
                self.widgets['edit_button'].configure(state="normal")
                self.widgets['delete_button'].configure(state="normal")
    
    def display_user_details(self, user):
        """عرض تفاصيل المستخدم"""
        # مسح النموذج
        self.clear_form()
        
        # ملء البيانات
        self.widgets['username_entry'].insert(0, user.username)
        self.widgets['full_name_entry'].insert(0, user.full_name)
        if user.email:
            self.widgets['email_entry'].insert(0, user.email)
        
        # تعيين الدور
        if user.role in self.roles:
            role_index = self.roles.index(user.role)
            role_names = [get_text(role, self.current_language) for role in self.roles]
            self.widgets['role_combobox'].set(role_names[role_index])
        
        # تعيين الفرع
        if user.branch_id and self.branches:
            branch = next((b for b in self.branches if b.id == user.branch_id), None)
            if branch:
                self.widgets['branch_combobox'].set(branch.name)
        
        # تعيين الصلاحيات
        if user.permissions:
            self.widgets['permissions_entry'].insert("1.0", user.permissions)
        
        # تعيين الحالة
        if user.is_active:
            self.widgets['is_active_checkbox'].select()
        else:
            self.widgets['is_active_checkbox'].deselect()
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        self.selected_user = None
        self.clear_form()
        self.set_form_mode(True)
    
    def edit_user(self, event=None):
        """تعديل المستخدم المحدد"""
        if hasattr(self, 'selected_user') and self.selected_user:
            self.set_form_mode(True)
        else:
            self.show_message("يرجى تحديد مستخدم للتعديل", "تنبيه", "warning")
    
    def delete_user(self):
        """حذف المستخدم المحدد"""
        if not hasattr(self, 'selected_user') or not self.selected_user:
            self.show_message("يرجى تحديد مستخدم للحذف", "تنبيه", "warning")
            return
        
        # منع حذف المستخدم الحالي
        if self.selected_user.id == auth_controller.current_user.id:
            self.show_message("لا يمكن حذف المستخدم الحالي", "خطأ", "error")
            return
        
        # تأكيد الحذف
        if self.show_confirmation(f"هل أنت متأكد من حذف المستخدم '{self.selected_user.full_name}'؟"):
            try:
                if self.selected_user.delete():
                    self.show_message("تم حذف المستخدم بنجاح", "نجح", "success")
                    self.refresh_data()
                    self.clear_form()
                    self.selected_user = None
                    
                    # تعطيل أزرار التحرير والحذف
                    self.widgets['edit_button'].configure(state="disabled")
                    self.widgets['delete_button'].configure(state="disabled")
                else:
                    self.show_message("فشل في حذف المستخدم", "خطأ", "error")
            except Exception as e:
                self.show_message(f"خطأ في حذف المستخدم: {str(e)}", "خطأ", "error")
    
    def save_user(self):
        """حفظ بيانات المستخدم"""
        try:
            # التحقق من صحة البيانات
            is_valid, errors = self.validate_form()
            if not is_valid:
                self.show_message("\n".join(errors), "خطأ في البيانات", "error")
                return
            
            # إنشاء أو تحديث المستخدم
            if self.selected_user:
                user = self.selected_user
            else:
                user = User()
            
            # تعيين البيانات
            user.username = self.widgets['username_entry'].get().strip()
            user.full_name = self.widgets['full_name_entry'].get().strip()
            user.email = self.widgets['email_entry'].get().strip() or None
            
            # تعيين كلمة المرور للمستخدمين الجدد أو إذا تم تغييرها
            password = self.widgets['password_entry'].get().strip()
            if password and (not self.selected_user or password != "********"):
                user.set_password(password)
            
            # تعيين الدور
            role_name = self.widgets['role_combobox'].get()
            role_names = [get_text(role, self.current_language) for role in self.roles]
            if role_name in role_names:
                role_index = role_names.index(role_name)
                user.role = self.roles[role_index]
            
            # تعيين الفرع
            branch_name = self.widgets['branch_combobox'].get()
            if branch_name and self.branches:
                branch = next((b for b in self.branches if b.name == branch_name), None)
                if branch:
                    user.branch_id = branch.id
            
            # تعيين الصلاحيات
            permissions = self.widgets['permissions_entry'].get("1.0", "end-1c").strip()
            user.permissions = permissions or None
            
            # تعيين الحالة
            user.is_active = self.widgets['is_active_checkbox'].get()
            
            # حفظ المستخدم
            if user.save():
                self.show_message("تم حفظ المستخدم بنجاح", "نجح", "success")
                self.refresh_data()
                self.set_form_mode(False)
            else:
                self.show_message("فشل في حفظ المستخدم", "خطأ", "error")
        
        except Exception as e:
            self.show_message(f"خطأ في حفظ المستخدم: {str(e)}", "خطأ", "error")
    
    def cancel_edit(self):
        """إلغاء التعديل"""
        self.set_form_mode(False)
        if hasattr(self, 'selected_user') and self.selected_user:
            self.display_user_details(self.selected_user)
        else:
            self.clear_form()
    
    def set_form_mode(self, edit_mode):
        """تعيين وضع النموذج"""
        state = "normal" if edit_mode else "disabled"
        
        # تفعيل/تعطيل الحقول
        for widget_name in ['username_entry', 'password_entry', 'full_name_entry', 
                           'email_entry', 'role_combobox', 'branch_combobox', 
                           'permissions_entry', 'is_active_checkbox']:
            if widget_name in self.widgets:
                self.widgets[widget_name].configure(state=state)
        
        # تفعيل/تعطيل أزرار النموذج
        self.widgets['save_button'].configure(state="normal" if edit_mode else "disabled")
        self.widgets['cancel_button'].configure(state="normal" if edit_mode else "disabled")
        
        # تعطيل/تفعيل أزرار القائمة
        list_state = "disabled" if edit_mode else "normal"
        self.widgets['add_button'].configure(state=list_state)
        if not edit_mode:
            self.widgets['edit_button'].configure(state="disabled")
            self.widgets['delete_button'].configure(state="disabled")
    
    def validate_form(self):
        """التحقق من صحة النموذج"""
        errors = []
        
        # اسم المستخدم
        username = self.widgets['username_entry'].get().strip()
        if not username:
            errors.append("اسم المستخدم مطلوب")
        elif len(username) < 3:
            errors.append("اسم المستخدم يجب أن يكون 3 أحرف على الأقل")
        else:
            # التحقق من تفرد اسم المستخدم
            existing_user = User.find_by_username(username)
            if existing_user and (not self.selected_user or existing_user.id != self.selected_user.id):
                errors.append("اسم المستخدم موجود مسبقاً")
        
        # كلمة المرور (مطلوبة للمستخدمين الجدد فقط)
        password = self.widgets['password_entry'].get().strip()
        if not self.selected_user and not password:
            errors.append("كلمة المرور مطلوبة للمستخدمين الجدد")
        elif password and len(password) < 6:
            errors.append("كلمة المرور يجب أن تكون 6 أحرف على الأقل")
        
        # الاسم الكامل
        full_name = self.widgets['full_name_entry'].get().strip()
        if not full_name:
            errors.append("الاسم الكامل مطلوب")
        
        # البريد الإلكتروني (اختياري ولكن يجب أن يكون صحيحاً)
        email = self.widgets['email_entry'].get().strip()
        if email:
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                errors.append("البريد الإلكتروني غير صحيح")
        
        # الدور
        role = self.widgets['role_combobox'].get()
        if not role:
            errors.append("دور المستخدم مطلوب")
        
        return len(errors) == 0, errors
    
    def on_search(self, event):
        """حدث البحث"""
        search_term = self.widgets['search_entry'].get().strip().lower()
        
        if not search_term:
            self.display_users(self.users_data)
        else:
            # تصفية المستخدمين
            filtered_users = []
            for user in self.users_data:
                if (search_term in user.full_name.lower() or 
                    search_term in user.username.lower() or
                    (user.email and search_term in user.email.lower())):
                    filtered_users.append(user)
            
            self.display_users(filtered_users)
    
    def clear_form(self):
        """مسح النموذج"""
        # مسح الحقول النصية
        for widget_name in ['username_entry', 'password_entry', 'full_name_entry', 'email_entry']:
            if widget_name in self.widgets:
                self.widgets[widget_name].delete(0, 'end')
        
        # مسح القوائم المنسدلة
        for widget_name in ['role_combobox', 'branch_combobox']:
            if widget_name in self.widgets:
                self.widgets[widget_name].set("")
        
        # مسح النص المتعدد الأسطر
        if 'permissions_entry' in self.widgets:
            self.widgets['permissions_entry'].delete("1.0", "end")
        
        # إلغاء تحديد الخانة
        if 'is_active_checkbox' in self.widgets:
            self.widgets['is_active_checkbox'].deselect()
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()
        self.widgets['search_entry'].delete(0, 'end')
    
    def apply_translations(self):
        """تطبيق الترجمات"""
        self.title(get_text('users', self.current_language))
