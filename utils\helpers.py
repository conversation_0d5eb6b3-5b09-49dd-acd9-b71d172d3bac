"""
دوال مساعدة عامة
"""
import os
import json
import csv
from datetime import datetime, date
import re

def format_currency(amount, currency_symbol="ريال"):
    """تنسيق العملة"""
    try:
        return f"{amount:,.2f} {currency_symbol}"
    except:
        return f"0.00 {currency_symbol}"

def format_date(date_obj, format_str="%Y-%m-%d"):
    """تنسيق التاريخ"""
    if isinstance(date_obj, str):
        try:
            date_obj = datetime.strptime(date_obj, "%Y-%m-%d %H:%M:%S")
        except:
            return date_obj
    
    if isinstance(date_obj, (datetime, date)):
        return date_obj.strftime(format_str)
    
    return str(date_obj)

def format_datetime(datetime_obj, format_str="%Y-%m-%d %H:%M:%S"):
    """تنسيق التاريخ والوقت"""
    return format_date(datetime_obj, format_str)

def validate_email(email):
    """التحقق من صحة البريد الإلكتروني"""
    if not email:
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone(phone):
    """التحقق من صحة رقم الهاتف"""
    if not phone:
        return False
    
    # إزالة المسافات والرموز
    clean_phone = re.sub(r'[^\d+]', '', phone)
    
    # فحص الطول والتنسيق
    if len(clean_phone) >= 10:
        return True
    
    return False

def generate_invoice_number(invoice_type="INV", branch_id=1):
    """إنشاء رقم فاتورة تلقائي"""
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    return f"{invoice_type}-{branch_id:03d}-{timestamp}"

def generate_product_code(category="PRD"):
    """إنشاء كود منتج تلقائي"""
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    return f"{category}-{timestamp}"

def calculate_tax(amount, tax_rate=15):
    """حساب الضريبة"""
    try:
        return (float(amount) * float(tax_rate)) / 100
    except:
        return 0.0

def calculate_discount(amount, discount_rate):
    """حساب الخصم"""
    try:
        if discount_rate > 100:
            discount_rate = discount_rate / 100
        return float(amount) * float(discount_rate)
    except:
        return 0.0

def safe_float(value, default=0.0):
    """تحويل آمن إلى رقم عشري"""
    try:
        return float(value)
    except:
        return default

def safe_int(value, default=0):
    """تحويل آمن إلى رقم صحيح"""
    try:
        return int(value)
    except:
        return default

def export_to_csv(data, filename, headers=None):
    """تصدير البيانات إلى CSV"""
    try:
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            if headers:
                writer = csv.DictWriter(csvfile, fieldnames=headers)
                writer.writeheader()
                for row in data:
                    writer.writerow(row)
            else:
                writer = csv.writer(csvfile)
                for row in data:
                    writer.writerow(row)
        return True
    except Exception as e:
        print(f"Error exporting to CSV: {e}")
        return False

def export_to_json(data, filename):
    """تصدير البيانات إلى JSON"""
    try:
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(data, jsonfile, ensure_ascii=False, indent=2, default=str)
        return True
    except Exception as e:
        print(f"Error exporting to JSON: {e}")
        return False

def import_from_csv(filename):
    """استيراد البيانات من CSV"""
    try:
        data = []
        with open(filename, 'r', encoding='utf-8-sig') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                data.append(row)
        return data
    except Exception as e:
        print(f"Error importing from CSV: {e}")
        return []

def create_backup_filename(prefix="backup", extension="db"):
    """إنشاء اسم ملف النسخة الاحتياطية"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"{prefix}_{timestamp}.{extension}"

def get_file_size(filepath):
    """الحصول على حجم الملف"""
    try:
        return os.path.getsize(filepath)
    except:
        return 0

def format_file_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.2f} {size_names[i]}"

def clean_string(text):
    """تنظيف النص"""
    if not text:
        return ""
    
    # إزالة المسافات الزائدة
    text = text.strip()
    
    # إزالة الأسطر الفارغة المتعددة
    text = re.sub(r'\n\s*\n', '\n\n', text)
    
    return text

def truncate_string(text, max_length=50, suffix="..."):
    """اقتطاع النص"""
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def get_age_from_date(birth_date):
    """حساب العمر من تاريخ الميلاد"""
    try:
        if isinstance(birth_date, str):
            birth_date = datetime.strptime(birth_date, "%Y-%m-%d").date()
        
        today = date.today()
        age = today.year - birth_date.year
        
        if today.month < birth_date.month or (today.month == birth_date.month and today.day < birth_date.day):
            age -= 1
        
        return age
    except:
        return 0

def generate_password(length=8):
    """إنشاء كلمة مرور عشوائية"""
    import random
    import string
    
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))

def hash_password(password):
    """تشفير كلمة المرور"""
    import bcrypt
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def verify_password(password, hashed):
    """التحقق من كلمة المرور"""
    import bcrypt
    try:
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    except:
        return False

def get_system_info():
    """الحصول على معلومات النظام"""
    import platform
    import psutil
    
    return {
        'platform': platform.system(),
        'platform_version': platform.version(),
        'architecture': platform.architecture()[0],
        'processor': platform.processor(),
        'python_version': platform.python_version(),
        'memory_total': psutil.virtual_memory().total,
        'memory_available': psutil.virtual_memory().available,
        'disk_usage': psutil.disk_usage('/').percent if platform.system() != 'Windows' else psutil.disk_usage('C:').percent
    }

def log_error(error, context=""):
    """تسجيل الأخطاء"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    error_msg = f"[{timestamp}] {context}: {str(error)}\n"
    
    try:
        with open("logs/errors.log", "a", encoding="utf-8") as f:
            f.write(error_msg)
    except:
        print(error_msg)

def create_directories(paths):
    """إنشاء المجلدات"""
    for path in paths:
        try:
            os.makedirs(path, exist_ok=True)
        except Exception as e:
            print(f"Error creating directory {path}: {e}")

def is_valid_date(date_string, format_str="%Y-%m-%d"):
    """فحص صحة التاريخ"""
    try:
        datetime.strptime(date_string, format_str)
        return True
    except:
        return False

def get_quarter_from_date(date_obj):
    """الحصول على الربع من التاريخ"""
    if isinstance(date_obj, str):
        try:
            date_obj = datetime.strptime(date_obj, "%Y-%m-%d")
        except:
            return 1
    
    month = date_obj.month
    if month <= 3:
        return 1
    elif month <= 6:
        return 2
    elif month <= 9:
        return 3
    else:
        return 4

def get_fiscal_year(date_obj=None):
    """الحصول على السنة المالية"""
    if date_obj is None:
        date_obj = datetime.now()
    
    if isinstance(date_obj, str):
        try:
            date_obj = datetime.strptime(date_obj, "%Y-%m-%d")
        except:
            date_obj = datetime.now()
    
    # افتراض أن السنة المالية تبدأ في أبريل
    if date_obj.month >= 4:
        return date_obj.year
    else:
        return date_obj.year - 1
