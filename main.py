"""
نقطة البداية الرئيسية لنظام إدارة الأعمال المتكامل
"""
import sys
import os
import tkinter as tk
from tkinter import messagebox
import customtkinter as ctk

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """فحص المتطلبات المطلوبة"""
    missing_packages = []
    
    try:
        import customtkinter
    except ImportError:
        missing_packages.append("customtkinter")
    
    try:
        import matplotlib
    except ImportError:
        missing_packages.append("matplotlib")
    
    try:
        import pandas
    except ImportError:
        missing_packages.append("pandas")
    
    try:
        import bcrypt
    except ImportError:
        missing_packages.append("bcrypt")
    
    # فحص المكتبات الاختيارية للنص العربي
    try:
        import arabic_reshaper
        import bidi
    except ImportError:
        print("تحذير: مكتبات دعم النص العربي غير مثبتة. سيتم استخدام النص العادي.")
    
    if missing_packages:
        error_msg = f"""
المتطلبات التالية غير مثبتة:
{', '.join(missing_packages)}

يرجى تثبيتها باستخدام:
pip install {' '.join(missing_packages)}
        """
        messagebox.showerror("خطأ في المتطلبات", error_msg)
        return False
    
    return True

def setup_environment():
    """إعداد البيئة"""
    try:
        # إنشاء المجلدات المطلوبة
        from config.settings import ensure_directories
        ensure_directories()
        
        # إعداد قاعدة البيانات
        from config.database import db_manager
        print("تم إعداد قاعدة البيانات بنجاح")
        
        # إعداد المظهر
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        return True
    
    except Exception as e:
        messagebox.showerror("خطأ في الإعداد", f"فشل في إعداد البيئة: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    try:
        # فحص المتطلبات
        if not check_dependencies():
            return
        
        # إعداد البيئة
        if not setup_environment():
            return
        
        # إنشاء النافذة الرئيسية المخفية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        # فتح شاشة تسجيل الدخول
        from views.login_view import LoginView
        login_window = LoginView()
        
        # بدء التطبيق
        login_window.mainloop()
    
    except KeyboardInterrupt:
        print("\nتم إيقاف التطبيق بواسطة المستخدم")
    
    except Exception as e:
        error_msg = f"خطأ غير متوقع: {str(e)}"
        print(error_msg)
        try:
            messagebox.showerror("خطأ", error_msg)
        except:
            pass
    
    finally:
        # تنظيف الموارد
        try:
            # إغلاق اتصالات قاعدة البيانات
            from config.database import db_manager
            # يمكن إضافة كود تنظيف إضافي هنا
        except:
            pass

if __name__ == "__main__":
    main()
