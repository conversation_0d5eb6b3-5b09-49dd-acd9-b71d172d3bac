"""
دعم النص من اليمين لليسار (RTL) للغة العربية
"""
import tkinter as tk
from tkinter import ttk
import customtkinter as ctk

try:
    from bidi.algorithm import get_display
    import arabic_reshaper
    BIDI_AVAILABLE = True
except ImportError:
    BIDI_AVAILABLE = False

class RTLSupport:
    """فئة دعم النص العربي والاتجاه من اليمين لليسار"""
    
    @staticmethod
    def reshape_arabic_text(text):
        """إعادة تشكيل النص العربي للعرض الصحيح"""
        if not BIDI_AVAILABLE or not text:
            return text
        
        try:
            # إعادة تشكيل النص العربي
            reshaped_text = arabic_reshaper.reshape(text)
            # تطبيق خوارزمية BiDi
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except:
            return text
    
    @staticmethod
    def is_arabic_text(text):
        """فحص ما إذا كان النص يحتوي على أحرف عربية"""
        if not text:
            return False
        
        arabic_chars = set(range(0x0600, 0x06FF + 1))  # نطاق الأحرف العربية
        return any(ord(char) in arabic_chars for char in text)
    
    @staticmethod
    def configure_widget_rtl(widget, text="", language="ar"):
        """تكوين عنصر الواجهة لدعم RTL"""
        if not text:
            return
        
        # إعادة تشكيل النص العربي
        if language == "ar" and RTLSupport.is_arabic_text(text):
            display_text = RTLSupport.reshape_arabic_text(text)
        else:
            display_text = text
        
        # تعيين النص
        if hasattr(widget, 'configure'):
            if isinstance(widget, (ctk.CTkLabel, ctk.CTkButton, ctk.CTkCheckBox)):
                widget.configure(text=display_text)
            elif isinstance(widget, (ctk.CTkEntry, ctk.CTkTextbox)):
                if hasattr(widget, 'delete'):
                    widget.delete(0, 'end')
                if hasattr(widget, 'insert'):
                    widget.insert(0, display_text)
        
        # تعيين اتجاه النص
        if language == "ar":
            try:
                if isinstance(widget, (tk.Entry, tk.Text, tk.Label)):
                    widget.configure(justify='right')
                elif isinstance(widget, ttk.Entry):
                    widget.configure(justify='right')
            except:
                pass

class RTLFrame(ctk.CTkFrame):
    """إطار مخصص لدعم RTL"""
    
    def __init__(self, master, language="ar", **kwargs):
        super().__init__(master, **kwargs)
        self.language = language
        
        # تكوين الشبكة للدعم RTL
        if language == "ar":
            self.grid_columnconfigure(0, weight=1)

class RTLLabel(ctk.CTkLabel):
    """تسمية مخصصة لدعم RTL"""
    
    def __init__(self, master, text="", language="ar", **kwargs):
        # إعادة تشكيل النص العربي
        if language == "ar" and RTLSupport.is_arabic_text(text):
            display_text = RTLSupport.reshape_arabic_text(text)
        else:
            display_text = text
        
        # تعيين المحاذاة للعربية
        if language == "ar":
            kwargs.setdefault('anchor', 'e')  # محاذاة لليمين
        
        super().__init__(master, text=display_text, **kwargs)
        self.language = language
        self.original_text = text
    
    def set_text(self, text):
        """تعيين نص جديد مع دعم RTL"""
        self.original_text = text
        if self.language == "ar" and RTLSupport.is_arabic_text(text):
            display_text = RTLSupport.reshape_arabic_text(text)
        else:
            display_text = text
        
        self.configure(text=display_text)

class RTLButton(ctk.CTkButton):
    """زر مخصص لدعم RTL"""
    
    def __init__(self, master, text="", language="ar", **kwargs):
        # إعادة تشكيل النص العربي
        if language == "ar" and RTLSupport.is_arabic_text(text):
            display_text = RTLSupport.reshape_arabic_text(text)
        else:
            display_text = text
        
        super().__init__(master, text=display_text, **kwargs)
        self.language = language
        self.original_text = text
    
    def set_text(self, text):
        """تعيين نص جديد مع دعم RTL"""
        self.original_text = text
        if self.language == "ar" and RTLSupport.is_arabic_text(text):
            display_text = RTLSupport.reshape_arabic_text(text)
        else:
            display_text = text
        
        self.configure(text=display_text)

class RTLEntry(ctk.CTkEntry):
    """حقل إدخال مخصص لدعم RTL"""
    
    def __init__(self, master, language="ar", **kwargs):
        super().__init__(master, **kwargs)
        self.language = language
        
        # تعيين اتجاه النص للعربية
        if language == "ar":
            self.configure(justify='right')
    
    def get_text(self):
        """الحصول على النص مع دعم RTL"""
        text = self.get()
        if self.language == "ar" and RTLSupport.is_arabic_text(text):
            return RTLSupport.reshape_arabic_text(text)
        return text
    
    def set_text(self, text):
        """تعيين النص مع دعم RTL"""
        self.delete(0, 'end')
        if text:
            if self.language == "ar" and RTLSupport.is_arabic_text(text):
                display_text = RTLSupport.reshape_arabic_text(text)
            else:
                display_text = text
            self.insert(0, display_text)

class RTLTextbox(ctk.CTkTextbox):
    """صندوق نص مخصص لدعم RTL"""
    
    def __init__(self, master, language="ar", **kwargs):
        super().__init__(master, **kwargs)
        self.language = language
    
    def get_text(self):
        """الحصول على النص مع دعم RTL"""
        text = self.get("1.0", "end-1c")
        if self.language == "ar" and RTLSupport.is_arabic_text(text):
            return RTLSupport.reshape_arabic_text(text)
        return text
    
    def set_text(self, text):
        """تعيين النص مع دعم RTL"""
        self.delete("1.0", "end")
        if text:
            if self.language == "ar" and RTLSupport.is_arabic_text(text):
                display_text = RTLSupport.reshape_arabic_text(text)
            else:
                display_text = text
            self.insert("1.0", display_text)

class RTLComboBox(ctk.CTkComboBox):
    """قائمة منسدلة مخصصة لدعم RTL"""
    
    def __init__(self, master, values=None, language="ar", **kwargs):
        # إعادة تشكيل قيم القائمة للعربية
        if values and language == "ar":
            display_values = []
            for value in values:
                if RTLSupport.is_arabic_text(str(value)):
                    display_values.append(RTLSupport.reshape_arabic_text(str(value)))
                else:
                    display_values.append(str(value))
            kwargs['values'] = display_values
        elif values:
            kwargs['values'] = values
        
        super().__init__(master, **kwargs)
        self.language = language
        self.original_values = values or []
    
    def set_values(self, values):
        """تعيين قيم جديدة مع دعم RTL"""
        self.original_values = values
        if values and self.language == "ar":
            display_values = []
            for value in values:
                if RTLSupport.is_arabic_text(str(value)):
                    display_values.append(RTLSupport.reshape_arabic_text(str(value)))
                else:
                    display_values.append(str(value))
            self.configure(values=display_values)
        else:
            self.configure(values=values)

def setup_rtl_font(widget, language="ar"):
    """إعداد الخط المناسب للغة"""
    from config.settings import FONTS
    
    if language == "ar":
        font_config = FONTS['arabic']
        font_family = font_config['family']
        font_size = font_config['size']
    else:
        font_config = FONTS['english']
        font_family = font_config['family']
        font_size = font_config['size']
    
    try:
        if hasattr(widget, 'configure'):
            widget.configure(font=(font_family, font_size))
    except:
        pass

def create_rtl_treeview(master, columns, language="ar"):
    """إنشاء Treeview مع دعم RTL"""
    tree = ttk.Treeview(master, columns=columns, show='headings')
    
    # تكوين الأعمدة
    for col in columns:
        tree.heading(col, text=col)
        tree.column(col, anchor='center' if language == "ar" else 'w')
    
    # إعداد الخط
    setup_rtl_font(tree, language)
    
    return tree

def pack_rtl(widget, side='right', **kwargs):
    """تعبئة العنصر مع مراعاة اتجاه RTL"""
    if side == 'left':
        side = 'right'
    elif side == 'right':
        side = 'left'
    
    widget.pack(side=side, **kwargs)

def grid_rtl(widget, row, column, language="ar", **kwargs):
    """وضع العنصر في الشبكة مع مراعاة اتجاه RTL"""
    if language == "ar":
        # عكس ترتيب الأعمدة للعربية
        max_column = kwargs.get('columnspan', 1) - 1
        column = max_column - column if max_column > 0 else column
    
    widget.grid(row=row, column=column, **kwargs)
