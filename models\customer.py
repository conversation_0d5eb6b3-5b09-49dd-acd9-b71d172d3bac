"""
نموذج العملاء
"""
from models.base_model import BaseModel

class Customer(BaseModel):
    """نموذج العميل"""
    
    table_name = "customers"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = kwargs.get('name', '')
        self.contact_person = kwargs.get('contact_person', '')
        self.phone = kwargs.get('phone', '')
        self.email = kwargs.get('email', '')
        self.address = kwargs.get('address', '')
        self.tax_number = kwargs.get('tax_number', '')
        self.payment_terms = kwargs.get('payment_terms', 30)
        self.credit_limit = kwargs.get('credit_limit', 0.0)
        self.current_balance = kwargs.get('current_balance', 0.0)
        self.is_active = kwargs.get('is_active', True)
        self.branch_id = kwargs.get('branch_id')
    
    @classmethod
    def search_customers(cls, search_term):
        """البحث في العملاء"""
        return cls.search(search_term, ['name', 'contact_person', 'phone', 'email'])
    
    @classmethod
    def get_active_customers(cls):
        """الحصول على العملاء النشطين"""
        return cls.find_all("is_active = 1", order_by="name")
    
    @classmethod
    def get_customers_by_branch(cls, branch_id):
        """الحصول على عملاء فرع معين"""
        return cls.find_all("branch_id = ? AND is_active = 1", [branch_id], order_by="name")
    
    def get_total_sales(self):
        """الحصول على إجمالي المبيعات لهذا العميل"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT COALESCE(SUM(total_amount), 0) as total
            FROM invoices 
            WHERE customer_id = ? AND invoice_type = 'sale'
        """, (self.id,))
        result = cursor.fetchone()
        conn.close()
        return result['total'] if result else 0.0
    
    def get_pending_invoices_count(self):
        """الحصول على عدد الفواتير المعلقة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM invoices 
            WHERE customer_id = ? AND invoice_type = 'sale' AND status = 'pending'
        """, (self.id,))
        result = cursor.fetchone()
        conn.close()
        return result['count'] if result else 0
    
    def get_overdue_amount(self):
        """الحصول على المبلغ المتأخر"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT COALESCE(SUM(total_amount - paid_amount), 0) as overdue
            FROM invoices 
            WHERE customer_id = ? AND invoice_type = 'sale' 
            AND status = 'overdue'
        """, (self.id,))
        result = cursor.fetchone()
        conn.close()
        return result['overdue'] if result else 0.0
    
    def validate(self):
        """التحقق من صحة بيانات العميل"""
        errors = []
        
        # التحقق من الاسم
        if not self.name or len(self.name.strip()) < 2:
            errors.append("اسم العميل مطلوب ويجب أن يكون حرفين على الأقل")
        
        # التحقق من البريد الإلكتروني
        if self.email:
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, self.email):
                errors.append("البريد الإلكتروني غير صحيح")
        
        # التحقق من شروط الدفع
        if self.payment_terms < 0:
            errors.append("شروط الدفع يجب أن تكون رقماً موجباً")
        
        # التحقق من حد الائتمان
        if self.credit_limit < 0:
            errors.append("حد الائتمان يجب أن يكون رقماً موجباً")
        
        return len(errors) == 0, errors
    
    def __str__(self):
        return self.name
