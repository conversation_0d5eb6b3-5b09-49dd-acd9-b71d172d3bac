"""
اختبار سريع للنظام
"""
import sys
import os

def test_imports():
    """اختبار الاستيرادات"""
    print("🔍 اختبار الاستيرادات...")
    
    try:
        import customtkinter as ctk
        print("✅ customtkinter")
    except ImportError as e:
        print(f"❌ customtkinter: {e}")
        return False
    
    try:
        import matplotlib
        print("✅ matplotlib")
    except ImportError as e:
        print(f"❌ matplotlib: {e}")
        return False
    
    try:
        import pandas
        print("✅ pandas")
    except ImportError as e:
        print(f"❌ pandas: {e}")
        return False
    
    try:
        import bcrypt
        print("✅ bcrypt")
    except ImportError as e:
        print(f"❌ bcrypt: {e}")
        return False
    
    return True

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️  اختبار قاعدة البيانات...")
    
    try:
        from config.database import db_manager
        
        # اختبار الاتصال
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        conn.close()
        
        print(f"✅ قاعدة البيانات تعمل - عدد المستخدمين: {user_count}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_models():
    """اختبار النماذج"""
    print("\n📊 اختبار النماذج...")
    
    try:
        from models.user import User
        from models.supplier import Supplier
        from models.customer import Customer
        from models.product import Product
        
        # اختبار المستخدم الافتراضي
        admin_user = User.find_by_username("admin")
        if admin_user:
            print(f"✅ المستخدم الافتراضي موجود: {admin_user.full_name}")
        else:
            print("❌ المستخدم الافتراضي غير موجود")
            return False
        
        print("✅ جميع النماذج تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في النماذج: {e}")
        return False

def test_authentication():
    """اختبار المصادقة"""
    print("\n🔐 اختبار المصادقة...")
    
    try:
        from controllers.auth_controller import auth_controller
        
        # اختبار تسجيل الدخول
        success, message = auth_controller.login("admin", "admin123")
        
        if success:
            print(f"✅ تسجيل الدخول نجح: {message}")
            
            # اختبار تسجيل الخروج
            auth_controller.logout()
            print("✅ تسجيل الخروج نجح")
            return True
        else:
            print(f"❌ فشل تسجيل الدخول: {message}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في المصادقة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("="*50)
    print("اختبار نظام إدارة الأعمال المتكامل")
    print("="*50)
    
    # اختبار الاستيرادات
    if not test_imports():
        print("\n❌ فشل في اختبار الاستيرادات")
        return
    
    # اختبار قاعدة البيانات
    if not test_database():
        print("\n❌ فشل في اختبار قاعدة البيانات")
        return
    
    # اختبار النماذج
    if not test_models():
        print("\n❌ فشل في اختبار النماذج")
        return
    
    # اختبار المصادقة
    if not test_authentication():
        print("\n❌ فشل في اختبار المصادقة")
        return
    
    print("\n" + "="*50)
    print("✅ جميع الاختبارات نجحت!")
    print("🚀 النظام جاهز للتشغيل")
    print("="*50)
    
    print("\n📋 معلومات تسجيل الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    
    print("\n🎯 لتشغيل النظام:")
    print("   python start_erp.py")
    print("   أو")
    print("   python main.py")

if __name__ == "__main__":
    main()
