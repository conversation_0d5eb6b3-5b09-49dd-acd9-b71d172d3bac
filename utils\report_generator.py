"""
مولد التقارير
"""
import os
from datetime import datetime, date
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_pdf import PdfPages
from config.database import db_manager
from config.settings import get_company_info
from utils.helpers import format_currency, format_date

class ReportGenerator:
    """مولد التقارير"""
    
    def __init__(self):
        self.company_info = get_company_info()
        
        # إعداد matplotlib للنص العربي
        plt.rcParams['font.family'] = ['Tahoma', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    
    def generate_sales_report(self, start_date, end_date, format_type='pdf'):
        """إنشاء تقرير المبيعات"""
        try:
            # الحصول على بيانات المبيعات
            conn = db_manager.get_connection()
            
            query = """
                SELECT 
                    i.invoice_date,
                    i.invoice_number,
                    c.name as customer_name,
                    i.subtotal,
                    i.tax_amount,
                    i.total_amount,
                    i.status
                FROM invoices i
                LEFT JOIN customers c ON i.customer_id = c.id
                WHERE i.invoice_type = 'sale' 
                AND i.invoice_date BETWEEN ? AND ?
                ORDER BY i.invoice_date DESC
            """
            
            df = pd.read_sql_query(query, conn, params=[start_date, end_date])
            conn.close()
            
            if df.empty:
                return False, "لا توجد بيانات مبيعات في الفترة المحددة"
            
            # إنشاء التقرير
            if format_type == 'pdf':
                return self._generate_sales_pdf_report(df, start_date, end_date)
            elif format_type == 'excel':
                return self._generate_sales_excel_report(df, start_date, end_date)
            else:
                return False, "نوع التقرير غير مدعوم"
        
        except Exception as e:
            return False, f"خطأ في إنشاء تقرير المبيعات: {str(e)}"
    
    def _generate_sales_pdf_report(self, df, start_date, end_date):
        """إنشاء تقرير المبيعات PDF"""
        filename = f"sales_report_{start_date}_{end_date}.pdf"
        filepath = os.path.join("reports", filename)
        
        # إنشاء مجلد التقارير
        os.makedirs("reports", exist_ok=True)
        
        with PdfPages(filepath) as pdf:
            # الصفحة الأولى - الملخص
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
            fig.suptitle(f'تقرير المبيعات من {start_date} إلى {end_date}', fontsize=16, fontweight='bold')
            
            # إجمالي المبيعات
            total_sales = df['total_amount'].sum()
            total_invoices = len(df)
            avg_invoice = df['total_amount'].mean()
            
            # رسم بياني للمبيعات اليومية
            daily_sales = df.groupby('invoice_date')['total_amount'].sum()
            ax1.plot(pd.to_datetime(daily_sales.index), daily_sales.values, marker='o')
            ax1.set_title('المبيعات اليومية')
            ax1.set_ylabel('المبلغ')
            ax1.tick_params(axis='x', rotation=45)
            
            # رسم بياني لحالة الفواتير
            status_counts = df['status'].value_counts()
            ax2.pie(status_counts.values, labels=status_counts.index, autopct='%1.1f%%')
            ax2.set_title('حالة الفواتير')
            
            # أفضل العملاء
            top_customers = df.groupby('customer_name')['total_amount'].sum().nlargest(5)
            ax3.bar(range(len(top_customers)), top_customers.values)
            ax3.set_title('أفضل 5 عملاء')
            ax3.set_xticks(range(len(top_customers)))
            ax3.set_xticklabels(top_customers.index, rotation=45)
            
            # إحصائيات عامة
            stats_text = f"""
            إجمالي المبيعات: {format_currency(total_sales)}
            عدد الفواتير: {total_invoices}
            متوسط الفاتورة: {format_currency(avg_invoice)}
            """
            ax4.text(0.1, 0.5, stats_text, fontsize=12, verticalalignment='center')
            ax4.axis('off')
            
            plt.tight_layout()
            pdf.savefig(fig, bbox_inches='tight')
            plt.close()
            
            # الصفحة الثانية - تفاصيل الفواتير
            fig, ax = plt.subplots(figsize=(12, 8))
            ax.axis('tight')
            ax.axis('off')
            
            # إنشاء جدول البيانات
            table_data = df[['invoice_date', 'invoice_number', 'customer_name', 'total_amount', 'status']].head(20)
            table = ax.table(cellText=table_data.values,
                           colLabels=['التاريخ', 'رقم الفاتورة', 'العميل', 'المبلغ', 'الحالة'],
                           cellLoc='center',
                           loc='center')
            
            table.auto_set_font_size(False)
            table.set_fontsize(9)
            table.scale(1.2, 1.5)
            
            ax.set_title('تفاصيل الفواتير (أول 20 فاتورة)', fontsize=14, fontweight='bold', pad=20)
            
            pdf.savefig(fig, bbox_inches='tight')
            plt.close()
        
        return True, filepath
    
    def _generate_sales_excel_report(self, df, start_date, end_date):
        """إنشاء تقرير المبيعات Excel"""
        filename = f"sales_report_{start_date}_{end_date}.xlsx"
        filepath = os.path.join("reports", filename)
        
        # إنشاء مجلد التقارير
        os.makedirs("reports", exist_ok=True)
        
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            # ورقة البيانات الأساسية
            df.to_excel(writer, sheet_name='تفاصيل المبيعات', index=False)
            
            # ورقة الملخص
            summary_data = {
                'المؤشر': ['إجمالي المبيعات', 'عدد الفواتير', 'متوسط الفاتورة'],
                'القيمة': [
                    df['total_amount'].sum(),
                    len(df),
                    df['total_amount'].mean()
                ]
            }
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='الملخص', index=False)
            
            # ورقة المبيعات اليومية
            daily_sales = df.groupby('invoice_date')['total_amount'].sum().reset_index()
            daily_sales.to_excel(writer, sheet_name='المبيعات اليومية', index=False)
            
            # ورقة أفضل العملاء
            top_customers = df.groupby('customer_name')['total_amount'].sum().nlargest(10).reset_index()
            top_customers.to_excel(writer, sheet_name='أفضل العملاء', index=False)
        
        return True, filepath
    
    def generate_profit_loss_report(self, start_date, end_date, format_type='pdf'):
        """إنشاء تقرير الأرباح والخسائر"""
        try:
            conn = db_manager.get_connection()
            
            # الإيرادات (المبيعات)
            revenue_query = """
                SELECT COALESCE(SUM(total_amount), 0) as revenue
                FROM invoices 
                WHERE invoice_type = 'sale' 
                AND invoice_date BETWEEN ? AND ?
                AND status != 'cancelled'
            """
            
            # المصروفات (المشتريات)
            expenses_query = """
                SELECT COALESCE(SUM(total_amount), 0) as expenses
                FROM invoices 
                WHERE invoice_type = 'purchase' 
                AND invoice_date BETWEEN ? AND ?
                AND status != 'cancelled'
            """
            
            revenue = pd.read_sql_query(revenue_query, conn, params=[start_date, end_date])['revenue'].iloc[0]
            expenses = pd.read_sql_query(expenses_query, conn, params=[start_date, end_date])['expenses'].iloc[0]
            
            conn.close()
            
            # حساب الربح/الخسارة
            profit_loss = revenue - expenses
            profit_margin = (profit_loss / revenue * 100) if revenue > 0 else 0
            
            # إنشاء التقرير
            if format_type == 'pdf':
                return self._generate_profit_loss_pdf(revenue, expenses, profit_loss, profit_margin, start_date, end_date)
            elif format_type == 'excel':
                return self._generate_profit_loss_excel(revenue, expenses, profit_loss, profit_margin, start_date, end_date)
            else:
                return False, "نوع التقرير غير مدعوم"
        
        except Exception as e:
            return False, f"خطأ في إنشاء تقرير الأرباح والخسائر: {str(e)}"
    
    def _generate_profit_loss_pdf(self, revenue, expenses, profit_loss, profit_margin, start_date, end_date):
        """إنشاء تقرير الأرباح والخسائر PDF"""
        filename = f"profit_loss_report_{start_date}_{end_date}.pdf"
        filepath = os.path.join("reports", filename)
        
        os.makedirs("reports", exist_ok=True)
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
        fig.suptitle(f'تقرير الأرباح والخسائر من {start_date} إلى {end_date}', fontsize=16, fontweight='bold')
        
        # رسم بياني للإيرادات والمصروفات
        categories = ['الإيرادات', 'المصروفات']
        values = [revenue, expenses]
        colors = ['green', 'red']
        
        ax1.bar(categories, values, color=colors, alpha=0.7)
        ax1.set_title('الإيرادات مقابل المصروفات')
        ax1.set_ylabel('المبلغ')
        
        # إضافة قيم على الأعمدة
        for i, v in enumerate(values):
            ax1.text(i, v + max(values) * 0.01, format_currency(v), ha='center', va='bottom')
        
        # معلومات الربح/الخسارة
        profit_text = f"""
        الإيرادات: {format_currency(revenue)}
        المصروفات: {format_currency(expenses)}
        {'الربح' if profit_loss >= 0 else 'الخسارة'}: {format_currency(abs(profit_loss))}
        هامش الربح: {profit_margin:.2f}%
        
        معلومات الشركة:
        {self.company_info['name']}
        {self.company_info['address']}
        {self.company_info['phone']}
        """
        
        ax2.text(0.1, 0.5, profit_text, fontsize=12, verticalalignment='center')
        ax2.axis('off')
        
        plt.tight_layout()
        plt.savefig(filepath, bbox_inches='tight')
        plt.close()
        
        return True, filepath
    
    def _generate_profit_loss_excel(self, revenue, expenses, profit_loss, profit_margin, start_date, end_date):
        """إنشاء تقرير الأرباح والخسائر Excel"""
        filename = f"profit_loss_report_{start_date}_{end_date}.xlsx"
        filepath = os.path.join("reports", filename)
        
        os.makedirs("reports", exist_ok=True)
        
        data = {
            'البند': ['الإيرادات', 'المصروفات', 'صافي الربح/الخسارة', 'هامش الربح %'],
            'المبلغ': [revenue, expenses, profit_loss, profit_margin]
        }
        
        df = pd.DataFrame(data)
        df.to_excel(filepath, index=False, sheet_name='الأرباح والخسائر')
        
        return True, filepath
    
    def generate_inventory_report(self, format_type='pdf'):
        """إنشاء تقرير المخزون"""
        try:
            conn = db_manager.get_connection()
            
            query = """
                SELECT 
                    code,
                    name,
                    category,
                    unit,
                    current_stock,
                    min_stock_level,
                    cost_price,
                    selling_price,
                    (current_stock * cost_price) as stock_value
                FROM products 
                WHERE is_active = 1
                ORDER BY name
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            if df.empty:
                return False, "لا توجد منتجات في المخزون"
            
            if format_type == 'pdf':
                return self._generate_inventory_pdf_report(df)
            elif format_type == 'excel':
                return self._generate_inventory_excel_report(df)
            else:
                return False, "نوع التقرير غير مدعوم"
        
        except Exception as e:
            return False, f"خطأ في إنشاء تقرير المخزون: {str(e)}"
    
    def _generate_inventory_pdf_report(self, df):
        """إنشاء تقرير المخزون PDF"""
        filename = f"inventory_report_{datetime.now().strftime('%Y%m%d')}.pdf"
        filepath = os.path.join("reports", filename)
        
        os.makedirs("reports", exist_ok=True)
        
        with PdfPages(filepath) as pdf:
            # الصفحة الأولى - الملخص
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
            fig.suptitle('تقرير المخزون', fontsize=16, fontweight='bold')
            
            # إجمالي قيمة المخزون
            total_value = df['stock_value'].sum()
            total_items = len(df)
            low_stock_items = len(df[df['current_stock'] <= df['min_stock_level']])
            
            # رسم بياني للفئات
            category_counts = df['category'].value_counts()
            ax1.pie(category_counts.values, labels=category_counts.index, autopct='%1.1f%%')
            ax1.set_title('توزيع المنتجات حسب الفئة')
            
            # رسم بياني لقيمة المخزون حسب الفئة
            category_values = df.groupby('category')['stock_value'].sum()
            ax2.bar(range(len(category_values)), category_values.values)
            ax2.set_title('قيمة المخزون حسب الفئة')
            ax2.set_xticks(range(len(category_values)))
            ax2.set_xticklabels(category_values.index, rotation=45)
            
            # المنتجات منخفضة المخزون
            low_stock_df = df[df['current_stock'] <= df['min_stock_level']].head(10)
            if not low_stock_df.empty:
                ax3.barh(range(len(low_stock_df)), low_stock_df['current_stock'])
                ax3.set_title('المنتجات منخفضة المخزون')
                ax3.set_yticks(range(len(low_stock_df)))
                ax3.set_yticklabels(low_stock_df['name'], fontsize=8)
            
            # إحصائيات عامة
            stats_text = f"""
            إجمالي قيمة المخزون: {format_currency(total_value)}
            عدد المنتجات: {total_items}
            منتجات منخفضة المخزون: {low_stock_items}
            """
            ax4.text(0.1, 0.5, stats_text, fontsize=12, verticalalignment='center')
            ax4.axis('off')
            
            plt.tight_layout()
            pdf.savefig(fig, bbox_inches='tight')
            plt.close()
        
        return True, filepath
    
    def _generate_inventory_excel_report(self, df):
        """إنشاء تقرير المخزون Excel"""
        filename = f"inventory_report_{datetime.now().strftime('%Y%m%d')}.xlsx"
        filepath = os.path.join("reports", filename)
        
        os.makedirs("reports", exist_ok=True)
        
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            # ورقة المخزون الكامل
            df.to_excel(writer, sheet_name='المخزون الكامل', index=False)
            
            # ورقة المنتجات منخفضة المخزون
            low_stock_df = df[df['current_stock'] <= df['min_stock_level']]
            low_stock_df.to_excel(writer, sheet_name='مخزون منخفض', index=False)
            
            # ورقة الملخص حسب الفئة
            category_summary = df.groupby('category').agg({
                'name': 'count',
                'current_stock': 'sum',
                'stock_value': 'sum'
            }).rename(columns={'name': 'عدد المنتجات', 'current_stock': 'إجمالي الكمية', 'stock_value': 'إجمالي القيمة'})
            category_summary.to_excel(writer, sheet_name='ملخص الفئات')
        
        return True, filepath

# إنشاء مثيل واحد من مولد التقارير
report_generator = ReportGenerator()
