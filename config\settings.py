"""
إعدادات النظام العامة
"""
import os
from datetime import datetime

# إعدادات التطبيق الأساسية
APP_NAME = "نظام إدارة الأعمال المتكامل"
APP_VERSION = "1.0.0"
APP_AUTHOR = "فريق التطوير"

# مسارات الملفات
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
DATABASE_PATH = os.path.join(BASE_DIR, "database", "erp_database.db")
BACKUP_DIR = os.path.join(BASE_DIR, "backups")
LOGS_DIR = os.path.join(BASE_DIR, "logs")
ASSETS_DIR = os.path.join(BASE_DIR, "assets")
ICONS_DIR = os.path.join(ASSETS_DIR, "icons")
IMAGES_DIR = os.path.join(ASSETS_DIR, "images")
FONTS_DIR = os.path.join(ASSETS_DIR, "fonts")

# إعدادات الواجهة الرسومية
WINDOW_MIN_WIDTH = 1200
WINDOW_MIN_HEIGHT = 800
WINDOW_DEFAULT_WIDTH = 1400
WINDOW_DEFAULT_HEIGHT = 900

# ألوان النظام
COLORS = {
    'primary': '#2E86AB',
    'secondary': '#A23B72',
    'success': '#28A745',
    'warning': '#FFC107',
    'danger': '#DC3545',
    'info': '#17A2B8',
    'light': '#F8F9FA',
    'dark': '#343A40',
    'white': '#FFFFFF',
    'gray': '#6C757D'
}

# إعدادات الخطوط
FONTS = {
    'arabic': {
        'family': 'Tahoma',
        'size': 12,
        'bold_size': 14
    },
    'english': {
        'family': 'Arial',
        'size': 11,
        'bold_size': 13
    }
}

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    'timeout': 30,
    'check_same_thread': False,
    'isolation_level': None
}

# إعدادات النسخ الاحتياطي
BACKUP_CONFIG = {
    'auto_backup': True,
    'backup_frequency_hours': 24,
    'max_backup_files': 30,
    'backup_format': 'sqlite'
}

# إعدادات الأمان
SECURITY_CONFIG = {
    'session_timeout_minutes': 60,
    'max_login_attempts': 5,
    'password_min_length': 6,
    'require_password_change_days': 90
}

# إعدادات التقارير
REPORTS_CONFIG = {
    'default_format': 'pdf',
    'page_size': 'A4',
    'orientation': 'portrait',
    'margin': 20
}

# إعدادات الإشعارات
NOTIFICATIONS_CONFIG = {
    'auto_delete_days': 30,
    'max_notifications_per_user': 100,
    'show_desktop_notifications': True
}

# إعدادات السجلات
LOGGING_CONFIG = {
    'level': 'INFO',
    'max_file_size_mb': 10,
    'backup_count': 5,
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
}

# إعدادات التصدير والاستيراد
EXPORT_CONFIG = {
    'formats': ['xlsx', 'csv', 'pdf'],
    'max_records_per_export': 10000,
    'temp_dir': os.path.join(BASE_DIR, 'temp')
}

# إعدادات الطباعة
PRINT_CONFIG = {
    'default_printer': None,
    'paper_size': 'A4',
    'orientation': 'portrait',
    'margins': {
        'top': 20,
        'bottom': 20,
        'left': 20,
        'right': 20
    }
}

# إعدادات العملة
CURRENCY_CONFIG = {
    'default_currency': 'SAR',
    'decimal_places': 2,
    'thousand_separator': ',',
    'decimal_separator': '.',
    'currency_symbol': 'ريال'
}

# إعدادات التاريخ والوقت
DATETIME_CONFIG = {
    'date_format': '%Y-%m-%d',
    'time_format': '%H:%M:%S',
    'datetime_format': '%Y-%m-%d %H:%M:%S',
    'timezone': 'Asia/Riyadh'
}

# إعدادات الشبكة
NETWORK_CONFIG = {
    'timeout': 30,
    'max_retries': 3,
    'retry_delay': 1
}

def ensure_directories():
    """إنشاء المجلدات المطلوبة إذا لم تكن موجودة"""
    directories = [
        os.path.dirname(DATABASE_PATH),
        BACKUP_DIR,
        LOGS_DIR,
        ASSETS_DIR,
        ICONS_DIR,
        IMAGES_DIR,
        FONTS_DIR,
        EXPORT_CONFIG['temp_dir']
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)

def get_setting(key, default=None):
    """الحصول على إعداد من قاعدة البيانات"""
    try:
        from config.database import db_manager
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT setting_value, setting_type FROM system_settings WHERE setting_key = ?", (key,))
        result = cursor.fetchone()
        conn.close()
        
        if result:
            value = result['setting_value']
            setting_type = result['setting_type']
            
            # تحويل القيمة حسب النوع
            if setting_type == 'boolean':
                return value.lower() == 'true'
            elif setting_type == 'number':
                try:
                    return float(value) if '.' in value else int(value)
                except:
                    return default
            elif setting_type == 'json':
                try:
                    import json
                    return json.loads(value)
                except:
                    return default
            else:
                return value
        
        return default
    except:
        return default

def set_setting(key, value, setting_type='string', description=None):
    """تعيين إعداد في قاعدة البيانات"""
    try:
        from config.database import db_manager
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # تحويل القيمة إلى نص
        if setting_type == 'boolean':
            value_str = 'true' if value else 'false'
        elif setting_type == 'json':
            import json
            value_str = json.dumps(value)
        else:
            value_str = str(value)
        
        cursor.execute("""
            INSERT OR REPLACE INTO system_settings 
            (setting_key, setting_value, setting_type, description, updated_at)
            VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
        """, (key, value_str, setting_type, description))
        
        conn.commit()
        conn.close()
        return True
    except:
        return False

def get_company_info():
    """الحصول على معلومات الشركة"""
    return {
        'name': get_setting('company_name', 'اسم الشركة'),
        'address': get_setting('company_address', 'عنوان الشركة'),
        'phone': get_setting('company_phone', 'هاتف الشركة'),
        'email': get_setting('company_email', '<EMAIL>'),
        'tax_number': get_setting('company_tax_number', ''),
        'logo': get_setting('company_logo', ''),
        'currency': get_setting('default_currency', 'SAR'),
        'tax_rate': get_setting('tax_rate', 15)
    }

def update_company_info(info):
    """تحديث معلومات الشركة"""
    settings = [
        ('company_name', info.get('name', ''), 'string', 'اسم الشركة'),
        ('company_address', info.get('address', ''), 'string', 'عنوان الشركة'),
        ('company_phone', info.get('phone', ''), 'string', 'هاتف الشركة'),
        ('company_email', info.get('email', ''), 'string', 'بريد الشركة'),
        ('company_tax_number', info.get('tax_number', ''), 'string', 'الرقم الضريبي'),
        ('company_logo', info.get('logo', ''), 'string', 'شعار الشركة'),
        ('default_currency', info.get('currency', 'SAR'), 'string', 'العملة الافتراضية'),
        ('tax_rate', info.get('tax_rate', 15), 'number', 'معدل الضريبة')
    ]
    
    success = True
    for key, value, setting_type, description in settings:
        if not set_setting(key, value, setting_type, description):
            success = False
    
    return success

# إنشاء المجلدات المطلوبة عند استيراد الملف
ensure_directories()
