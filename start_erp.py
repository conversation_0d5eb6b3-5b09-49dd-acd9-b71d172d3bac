#!/usr/bin/env python3
"""
ملف تشغيل نظام إدارة الأعمال المتكامل
"""
import sys
import os
import subprocess
import platform

def print_banner():
    """طباعة شعار النظام"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║           نظام إدارة الأعمال المتكامل                        ║
║              Integrated Business Management System           ║
║                                                              ║
║                        الإصدار 1.0.0                        ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """فحص إصدار Python"""
    print("🔍 فحص إصدار Python...")
    
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"   الإصدار الحالي: {sys.version}")
        print("   يرجى تحديث Python من: https://python.org")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - متوافق")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📦 فحص وتثبيت المتطلبات...")
    
    if not os.path.exists("requirements.txt"):
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    try:
        # فحص المكتبات المطلوبة
        required_packages = [
            "customtkinter",
            "matplotlib", 
            "pandas",
            "bcrypt",
            "Pillow"
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
                print(f"✅ {package} - مثبت")
            except ImportError:
                missing_packages.append(package)
                print(f"❌ {package} - غير مثبت")
        
        # تثبيت المكتبات المفقودة
        if missing_packages:
            print(f"\n🔄 تثبيت المكتبات المفقودة: {', '.join(missing_packages)}")
            
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ تم تثبيت جميع المتطلبات بنجاح")
                return True
            else:
                print("❌ فشل في تثبيت المتطلبات:")
                print(result.stderr)
                return False
        else:
            print("✅ جميع المتطلبات مثبتة مسبقاً")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def check_optional_packages():
    """فحص المكتبات الاختيارية"""
    print("\n🔍 فحص المكتبات الاختيارية...")
    
    optional_packages = {
        "arabic_reshaper": "دعم النص العربي",
        "bidi": "دعم اتجاه النص",
        "openpyxl": "تصدير Excel",
        "reportlab": "تقارير PDF"
    }
    
    for package, description in optional_packages.items():
        try:
            __import__(package)
            print(f"✅ {package} - {description}")
        except ImportError:
            print(f"⚠️  {package} - {description} (اختياري)")

def setup_directories():
    """إعداد المجلدات المطلوبة"""
    print("\n📁 إعداد المجلدات...")
    
    directories = [
        "database",
        "backups", 
        "logs",
        "assets/icons",
        "assets/images",
        "assets/fonts"
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ {directory}")
        except Exception as e:
            print(f"❌ فشل في إنشاء {directory}: {e}")
            return False
    
    return True

def check_database():
    """فحص قاعدة البيانات"""
    print("\n🗄️  فحص قاعدة البيانات...")
    
    try:
        from config.database import db_manager
        print("✅ تم إعداد قاعدة البيانات بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def show_login_info():
    """عرض معلومات تسجيل الدخول"""
    print("\n" + "="*60)
    print("🔐 معلومات تسجيل الدخول الافتراضية:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("="*60)

def start_application():
    """تشغيل التطبيق"""
    print("\n🚀 تشغيل النظام...")
    
    try:
        # تشغيل التطبيق الرئيسي
        import main
        main.main()
        
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف النظام بواسطة المستخدم")
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        print("\nتفاصيل الخطأ:")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    # تعيين ترميز UTF-8 للوحة التحكم
    if platform.system() == "Windows":
        os.system("chcp 65001 >nul")
    
    # طباعة الشعار
    print_banner()
    
    # فحص النظام
    if not check_python_version():
        input("\nاضغط Enter للخروج...")
        return
    
    if not install_requirements():
        input("\nاضغط Enter للخروج...")
        return
    
    check_optional_packages()
    
    if not setup_directories():
        input("\nاضغط Enter للخروج...")
        return
    
    if not check_database():
        input("\nاضغط Enter للخروج...")
        return
    
    # عرض معلومات تسجيل الدخول
    show_login_info()
    
    # تشغيل التطبيق
    start_application()
    
    print("\n👋 شكراً لاستخدام نظام إدارة الأعمال المتكامل")

if __name__ == "__main__":
    main()
