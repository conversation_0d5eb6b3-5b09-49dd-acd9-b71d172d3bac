"""
واجهة إعدادات النظام
"""
import customtkinter as ctk
from tkinter import messagebox, filedialog
import tkinter as tk
from config.languages import get_text, get_current_language, set_language
from config.settings import get_setting, set_setting, get_company_info, update_company_info
from utils.rtl_support import RTLFrame, RTLLabel, RTLButton, RTLEntry, RTLComboBox, RTLTextbox
from views.base_view import BaseView
from controllers.auth_controller import auth_controller
from controllers.backup_controller import backup_controller

class SettingsView(BaseView):
    """واجهة إعدادات النظام"""
    
    def __init__(self, parent=None):
        super().__init__(parent, "إعدادات النظام", 800, 600)
        
        # فحص الصلاحية
        if not self.check_permission('settings'):
            self.destroy()
            return
        
        # تحميل الإعدادات
        self.load_settings()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إنشاء الإطار الرئيسي
        main_frame = RTLFrame(self, language=self.current_language)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # عنوان الصفحة
        self.create_header_frame(main_frame, get_text('settings', self.current_language))
        
        # إنشاء التبويبات
        self.create_tabs(main_frame)
        
        # أزرار الحفظ والإلغاء
        self.create_action_buttons(main_frame)
    
    def create_tabs(self, parent):
        """إنشاء التبويبات"""
        # إنشاء إطار التبويبات
        self.notebook = ctk.CTkTabview(parent)
        self.notebook.pack(fill="both", expand=True, pady=10)
        
        # تبويب معلومات الشركة
        self.create_company_tab()
        
        # تبويب الإعدادات العامة
        self.create_general_tab()
        
        # تبويب النسخ الاحتياطي
        self.create_backup_tab()
        
        # تبويب المظهر واللغة
        self.create_appearance_tab()
        
        # تبويب الأمان
        self.create_security_tab()
    
    def create_company_tab(self):
        """إنشاء تبويب معلومات الشركة"""
        tab = self.notebook.add("معلومات الشركة")
        
        # إطار النموذج
        form_frame = RTLFrame(tab, language=self.current_language)
        form_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # اسم الشركة
        _, _, self.widgets['company_name'] = self.create_form_field(
            form_frame, "اسم الشركة", "entry"
        )
        
        # عنوان الشركة
        _, _, self.widgets['company_address'] = self.create_form_field(
            form_frame, "عنوان الشركة", "text"
        )
        
        # هاتف الشركة
        _, _, self.widgets['company_phone'] = self.create_form_field(
            form_frame, "هاتف الشركة", "entry"
        )
        
        # بريد الشركة
        _, _, self.widgets['company_email'] = self.create_form_field(
            form_frame, "بريد الشركة الإلكتروني", "entry"
        )
        
        # الرقم الضريبي
        _, _, self.widgets['company_tax_number'] = self.create_form_field(
            form_frame, "الرقم الضريبي", "entry"
        )
        
        # شعار الشركة
        logo_frame = RTLFrame(form_frame, language=self.current_language)
        logo_frame.pack(fill="x", padx=10, pady=5)
        
        RTLLabel(
            logo_frame,
            text="شعار الشركة",
            language=self.current_language,
            width=150
        ).pack(side="right" if self.current_language == "ar" else "left", padx=(0, 10))
        
        self.widgets['logo_path'] = RTLEntry(logo_frame, language=self.current_language, state="readonly")
        self.widgets['logo_path'].pack(side="left", fill="x", expand=True, padx=(0, 5))
        
        RTLButton(
            logo_frame,
            text="استعراض",
            language=self.current_language,
            command=self.browse_logo,
            width=80
        ).pack(side="left")
    
    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        tab = self.notebook.add("الإعدادات العامة")
        
        form_frame = RTLFrame(tab, language=self.current_language)
        form_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # العملة الافتراضية
        currencies = ['ريال سعودي', 'دولار أمريكي', 'يورو', 'جنيه مصري', 'درهم إماراتي']
        _, _, self.widgets['default_currency'] = self.create_form_field(
            form_frame, "العملة الافتراضية", "combobox", values=currencies
        )
        
        # معدل الضريبة
        _, _, self.widgets['tax_rate'] = self.create_form_field(
            form_frame, "معدل الضريبة المضافة (%)", "entry"
        )
        
        # تنسيق التاريخ
        date_formats = ['YYYY-MM-DD', 'DD/MM/YYYY', 'MM/DD/YYYY']
        _, _, self.widgets['date_format'] = self.create_form_field(
            form_frame, "تنسيق التاريخ", "combobox", values=date_formats
        )
        
        # عدد الأسطر في الصفحة
        _, _, self.widgets['page_size'] = self.create_form_field(
            form_frame, "عدد السجلات في الصفحة", "entry"
        )
        
        # مهلة انتهاء الجلسة
        _, _, self.widgets['session_timeout'] = self.create_form_field(
            form_frame, "مهلة انتهاء الجلسة (دقيقة)", "entry"
        )
    
    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطي"""
        tab = self.notebook.add("النسخ الاحتياطي")
        
        form_frame = RTLFrame(tab, language=self.current_language)
        form_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # تفعيل النسخ الاحتياطي التلقائي
        _, _, self.widgets['auto_backup'] = self.create_form_field(
            form_frame, "تفعيل النسخ الاحتياطي التلقائي", "checkbox"
        )
        
        # تكرار النسخ الاحتياطي
        _, _, self.widgets['backup_frequency'] = self.create_form_field(
            form_frame, "تكرار النسخ الاحتياطي (ساعة)", "entry"
        )
        
        # عدد النسخ الاحتياطية المحفوظة
        _, _, self.widgets['max_backup_files'] = self.create_form_field(
            form_frame, "عدد النسخ الاحتياطية المحفوظة", "entry"
        )
        
        # مجلد النسخ الاحتياطي
        backup_frame = RTLFrame(form_frame, language=self.current_language)
        backup_frame.pack(fill="x", padx=10, pady=5)
        
        RTLLabel(
            backup_frame,
            text="مجلد النسخ الاحتياطي",
            language=self.current_language,
            width=150
        ).pack(side="right" if self.current_language == "ar" else "left", padx=(0, 10))
        
        self.widgets['backup_path'] = RTLEntry(backup_frame, language=self.current_language, state="readonly")
        self.widgets['backup_path'].pack(side="left", fill="x", expand=True, padx=(0, 5))
        
        RTLButton(
            backup_frame,
            text="استعراض",
            language=self.current_language,
            command=self.browse_backup_path,
            width=80
        ).pack(side="left")
        
        # أزرار النسخ الاحتياطي
        backup_buttons_frame = RTLFrame(form_frame, language=self.current_language)
        backup_buttons_frame.pack(fill="x", pady=20)
        
        RTLButton(
            backup_buttons_frame,
            text="إنشاء نسخة احتياطية الآن",
            language=self.current_language,
            command=self.create_backup_now,
            width=200
        ).pack(side="right" if self.current_language == "ar" else "left", padx=5)
        
        RTLButton(
            backup_buttons_frame,
            text="استعادة نسخة احتياطية",
            language=self.current_language,
            command=self.restore_backup,
            width=200
        ).pack(side="right" if self.current_language == "ar" else "left", padx=5)
    
    def create_appearance_tab(self):
        """إنشاء تبويب المظهر واللغة"""
        tab = self.notebook.add("المظهر واللغة")
        
        form_frame = RTLFrame(tab, language=self.current_language)
        form_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # اللغة
        languages = ['العربية', 'English']
        _, _, self.widgets['language'] = self.create_form_field(
            form_frame, "لغة النظام", "combobox", values=languages
        )
        
        # المظهر
        themes = ['فاتح', 'داكن', 'تلقائي']
        _, _, self.widgets['theme'] = self.create_form_field(
            form_frame, "مظهر النظام", "combobox", values=themes
        )
        
        # حجم الخط
        font_sizes = ['صغير', 'متوسط', 'كبير']
        _, _, self.widgets['font_size'] = self.create_form_field(
            form_frame, "حجم الخط", "combobox", values=font_sizes
        )
        
        # عرض الإشعارات
        _, _, self.widgets['show_notifications'] = self.create_form_field(
            form_frame, "عرض الإشعارات", "checkbox"
        )
        
        # أصوات النظام
        _, _, self.widgets['system_sounds'] = self.create_form_field(
            form_frame, "تفعيل أصوات النظام", "checkbox"
        )
    
    def create_security_tab(self):
        """إنشاء تبويب الأمان"""
        tab = self.notebook.add("الأمان")
        
        form_frame = RTLFrame(tab, language=self.current_language)
        form_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # الحد الأقصى لمحاولات تسجيل الدخول
        _, _, self.widgets['max_login_attempts'] = self.create_form_field(
            form_frame, "الحد الأقصى لمحاولات تسجيل الدخول", "entry"
        )
        
        # مدة قفل الحساب
        _, _, self.widgets['lockout_duration'] = self.create_form_field(
            form_frame, "مدة قفل الحساب (دقيقة)", "entry"
        )
        
        # طول كلمة المرور الأدنى
        _, _, self.widgets['min_password_length'] = self.create_form_field(
            form_frame, "طول كلمة المرور الأدنى", "entry"
        )
        
        # تعقيد كلمة المرور
        _, _, self.widgets['password_complexity'] = self.create_form_field(
            form_frame, "تطبيق تعقيد كلمة المرور", "checkbox"
        )
        
        # تسجيل النشاطات
        _, _, self.widgets['audit_logging'] = self.create_form_field(
            form_frame, "تفعيل تسجيل النشاطات", "checkbox"
        )
        
        # مدة الاحتفاظ بالسجلات
        _, _, self.widgets['log_retention_days'] = self.create_form_field(
            form_frame, "مدة الاحتفاظ بالسجلات (يوم)", "entry"
        )
    
    def create_action_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        buttons_frame = self.create_button_frame(parent)
        
        RTLButton(
            buttons_frame,
            text="حفظ الإعدادات",
            language=self.current_language,
            command=self.save_settings,
            width=120
        ).pack(side="right" if self.current_language == "ar" else "left", padx=5)
        
        RTLButton(
            buttons_frame,
            text="استعادة الافتراضي",
            language=self.current_language,
            command=self.reset_to_defaults,
            width=120
        ).pack(side="right" if self.current_language == "ar" else "left", padx=5)
        
        RTLButton(
            buttons_frame,
            text="إلغاء",
            language=self.current_language,
            command=self.on_closing,
            width=120
        ).pack(side="right" if self.current_language == "ar" else "left", padx=5)
    
    def load_settings(self):
        """تحميل الإعدادات الحالية"""
        try:
            # معلومات الشركة
            company_info = get_company_info()
            
            if hasattr(self, 'widgets'):
                # معلومات الشركة
                if 'company_name' in self.widgets:
                    self.widgets['company_name'].insert(0, company_info.get('name', ''))
                if 'company_address' in self.widgets:
                    self.widgets['company_address'].insert("1.0", company_info.get('address', ''))
                if 'company_phone' in self.widgets:
                    self.widgets['company_phone'].insert(0, company_info.get('phone', ''))
                if 'company_email' in self.widgets:
                    self.widgets['company_email'].insert(0, company_info.get('email', ''))
                if 'company_tax_number' in self.widgets:
                    self.widgets['company_tax_number'].insert(0, company_info.get('tax_number', ''))
                if 'logo_path' in self.widgets:
                    self.widgets['logo_path'].insert(0, company_info.get('logo', ''))
                
                # الإعدادات العامة
                if 'default_currency' in self.widgets:
                    self.widgets['default_currency'].set(get_setting('default_currency', 'ريال سعودي'))
                if 'tax_rate' in self.widgets:
                    self.widgets['tax_rate'].insert(0, str(get_setting('tax_rate', 15)))
                if 'page_size' in self.widgets:
                    self.widgets['page_size'].insert(0, str(get_setting('page_size', 20)))
                if 'session_timeout' in self.widgets:
                    self.widgets['session_timeout'].insert(0, str(get_setting('session_timeout', 60)))
                
                # النسخ الاحتياطي
                if 'auto_backup' in self.widgets:
                    if get_setting('auto_backup', True):
                        self.widgets['auto_backup'].select()
                if 'backup_frequency' in self.widgets:
                    self.widgets['backup_frequency'].insert(0, str(get_setting('backup_frequency_hours', 24)))
                if 'max_backup_files' in self.widgets:
                    self.widgets['max_backup_files'].insert(0, str(get_setting('max_backup_files', 30)))
                
                # المظهر واللغة
                if 'language' in self.widgets:
                    current_lang = get_setting('language', 'ar')
                    lang_display = 'العربية' if current_lang == 'ar' else 'English'
                    self.widgets['language'].set(lang_display)
                
                if 'theme' in self.widgets:
                    theme = get_setting('theme', 'light')
                    theme_display = {'light': 'فاتح', 'dark': 'داكن', 'system': 'تلقائي'}.get(theme, 'فاتح')
                    self.widgets['theme'].set(theme_display)
                
                # الأمان
                if 'max_login_attempts' in self.widgets:
                    self.widgets['max_login_attempts'].insert(0, str(get_setting('max_login_attempts', 5)))
                if 'min_password_length' in self.widgets:
                    self.widgets['min_password_length'].insert(0, str(get_setting('min_password_length', 6)))
                if 'log_retention_days' in self.widgets:
                    self.widgets['log_retention_days'].insert(0, str(get_setting('log_retention_days', 90)))
        
        except Exception as e:
            self.show_message(f"خطأ في تحميل الإعدادات: {str(e)}", "خطأ", "error")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # حفظ معلومات الشركة
            company_info = {
                'name': self.widgets['company_name'].get(),
                'address': self.widgets['company_address'].get("1.0", "end-1c"),
                'phone': self.widgets['company_phone'].get(),
                'email': self.widgets['company_email'].get(),
                'tax_number': self.widgets['company_tax_number'].get(),
                'logo': self.widgets['logo_path'].get()
            }
            
            if not update_company_info(company_info):
                self.show_message("فشل في حفظ معلومات الشركة", "خطأ", "error")
                return
            
            # حفظ الإعدادات العامة
            settings_to_save = [
                ('default_currency', self.widgets['default_currency'].get(), 'string'),
                ('tax_rate', float(self.widgets['tax_rate'].get() or 15), 'number'),
                ('page_size', int(self.widgets['page_size'].get() or 20), 'number'),
                ('session_timeout', int(self.widgets['session_timeout'].get() or 60), 'number'),
                ('auto_backup', self.widgets['auto_backup'].get(), 'boolean'),
                ('backup_frequency_hours', int(self.widgets['backup_frequency'].get() or 24), 'number'),
                ('max_backup_files', int(self.widgets['max_backup_files'].get() or 30), 'number'),
                ('max_login_attempts', int(self.widgets['max_login_attempts'].get() or 5), 'number'),
                ('min_password_length', int(self.widgets['min_password_length'].get() or 6), 'number'),
                ('log_retention_days', int(self.widgets['log_retention_days'].get() or 90), 'number')
            ]
            
            # حفظ اللغة
            lang_display = self.widgets['language'].get()
            language_code = 'ar' if lang_display == 'العربية' else 'en'
            settings_to_save.append(('language', language_code, 'string'))
            
            # حفظ المظهر
            theme_display = self.widgets['theme'].get()
            theme_code = {'فاتح': 'light', 'داكن': 'dark', 'تلقائي': 'system'}.get(theme_display, 'light')
            settings_to_save.append(('theme', theme_code, 'string'))
            
            # حفظ جميع الإعدادات
            for key, value, setting_type in settings_to_save:
                if not set_setting(key, value, setting_type):
                    self.show_message(f"فشل في حفظ الإعداد: {key}", "خطأ", "error")
                    return
            
            self.show_message("تم حفظ الإعدادات بنجاح", "نجح", "success")
            
            # تطبيق التغييرات
            self.apply_changes()
        
        except ValueError as e:
            self.show_message("يرجى التأكد من صحة القيم المدخلة", "خطأ في البيانات", "error")
        except Exception as e:
            self.show_message(f"خطأ في حفظ الإعدادات: {str(e)}", "خطأ", "error")
    
    def apply_changes(self):
        """تطبيق التغييرات"""
        try:
            # تطبيق تغيير اللغة
            lang_display = self.widgets['language'].get()
            new_language = 'ar' if lang_display == 'العربية' else 'en'
            
            if new_language != self.current_language:
                set_language(new_language)
                self.show_message("سيتم تطبيق تغيير اللغة عند إعادة تشغيل النظام", "معلومات", "info")
            
            # تطبيق تغيير المظهر
            theme_display = self.widgets['theme'].get()
            theme_code = {'فاتح': 'light', 'داكن': 'dark', 'تلقائي': 'system'}.get(theme_display, 'light')
            ctk.set_appearance_mode(theme_code)
            
        except Exception as e:
            print(f"Error applying changes: {e}")
    
    def reset_to_defaults(self):
        """استعادة الإعدادات الافتراضية"""
        if self.show_confirmation("هل أنت متأكد من استعادة الإعدادات الافتراضية؟"):
            try:
                # مسح جميع الحقول
                self.clear_all_fields()
                
                # تعيين القيم الافتراضية
                self.set_default_values()
                
                self.show_message("تم استعادة الإعدادات الافتراضية", "نجح", "success")
            
            except Exception as e:
                self.show_message(f"خطأ في استعادة الإعدادات: {str(e)}", "خطأ", "error")
    
    def clear_all_fields(self):
        """مسح جميع الحقول"""
        # مسح الحقول النصية
        text_fields = ['company_name', 'company_phone', 'company_email', 'company_tax_number',
                      'tax_rate', 'page_size', 'session_timeout', 'backup_frequency',
                      'max_backup_files', 'max_login_attempts', 'min_password_length',
                      'log_retention_days']
        
        for field in text_fields:
            if field in self.widgets:
                self.widgets[field].delete(0, 'end')
        
        # مسح النصوص متعددة الأسطر
        if 'company_address' in self.widgets:
            self.widgets['company_address'].delete("1.0", "end")
        
        # مسح القوائم المنسدلة
        combo_fields = ['default_currency', 'language', 'theme', 'font_size']
        for field in combo_fields:
            if field in self.widgets:
                self.widgets[field].set("")
        
        # إلغاء تحديد الخانات
        checkbox_fields = ['auto_backup', 'show_notifications', 'system_sounds',
                          'password_complexity', 'audit_logging']
        for field in checkbox_fields:
            if field in self.widgets:
                self.widgets[field].deselect()
    
    def set_default_values(self):
        """تعيين القيم الافتراضية"""
        defaults = {
            'company_name': 'اسم الشركة',
            'default_currency': 'ريال سعودي',
            'tax_rate': '15',
            'page_size': '20',
            'session_timeout': '60',
            'backup_frequency': '24',
            'max_backup_files': '30',
            'language': 'العربية',
            'theme': 'فاتح',
            'max_login_attempts': '5',
            'min_password_length': '6',
            'log_retention_days': '90'
        }
        
        for field, value in defaults.items():
            if field in self.widgets:
                if hasattr(self.widgets[field], 'insert'):
                    self.widgets[field].insert(0, value)
                elif hasattr(self.widgets[field], 'set'):
                    self.widgets[field].set(value)
        
        # تفعيل الخانات الافتراضية
        default_checkboxes = ['auto_backup', 'audit_logging']
        for field in default_checkboxes:
            if field in self.widgets:
                self.widgets[field].select()
    
    def browse_logo(self):
        """استعراض شعار الشركة"""
        file_path = filedialog.askopenfilename(
            title="اختيار شعار الشركة",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp")]
        )
        
        if file_path:
            self.widgets['logo_path'].configure(state="normal")
            self.widgets['logo_path'].delete(0, 'end')
            self.widgets['logo_path'].insert(0, file_path)
            self.widgets['logo_path'].configure(state="readonly")
    
    def browse_backup_path(self):
        """استعراض مجلد النسخ الاحتياطي"""
        folder_path = filedialog.askdirectory(title="اختيار مجلد النسخ الاحتياطي")
        
        if folder_path:
            self.widgets['backup_path'].configure(state="normal")
            self.widgets['backup_path'].delete(0, 'end')
            self.widgets['backup_path'].insert(0, folder_path)
            self.widgets['backup_path'].configure(state="readonly")
    
    def create_backup_now(self):
        """إنشاء نسخة احتياطية فورية"""
        try:
            success, result = backup_controller.create_backup()
            
            if success:
                self.show_message(f"تم إنشاء النسخة الاحتياطية بنجاح\nالموقع: {result}", "نجح", "success")
            else:
                self.show_message(f"فشل في إنشاء النسخة الاحتياطية: {result}", "خطأ", "error")
        
        except Exception as e:
            self.show_message(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}", "خطأ", "error")
    
    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        file_path = filedialog.askopenfilename(
            title="اختيار النسخة الاحتياطية",
            filetypes=[("Backup files", "*.zip")]
        )
        
        if file_path:
            if self.show_confirmation("هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\nسيتم استبدال البيانات الحالية."):
                try:
                    success, result = backup_controller.restore_backup(file_path)
                    
                    if success:
                        self.show_message("تم استعادة النسخة الاحتياطية بنجاح\nيرجى إعادة تشغيل النظام", "نجح", "success")
                    else:
                        self.show_message(f"فشل في استعادة النسخة الاحتياطية: {result}", "خطأ", "error")
                
                except Exception as e:
                    self.show_message(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}", "خطأ", "error")
    
    def apply_translations(self):
        """تطبيق الترجمات"""
        self.title(get_text('settings', self.current_language))
