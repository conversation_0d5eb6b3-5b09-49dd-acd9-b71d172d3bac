"""
دوال التحقق من صحة البيانات
"""
import re
from datetime import datetime, date

class ValidationError(Exception):
    """استثناء خطأ التحقق"""
    pass

class Validator:
    """فئة التحقق من البيانات"""
    
    @staticmethod
    def required(value, field_name="الحقل"):
        """التحقق من أن الحقل مطلوب"""
        if value is None or (isinstance(value, str) and not value.strip()):
            raise ValidationError(f"{field_name} مطلوب")
        return True
    
    @staticmethod
    def min_length(value, min_len, field_name="الحقل"):
        """التحقق من الحد الأدنى لطول النص"""
        if value and len(str(value)) < min_len:
            raise ValidationError(f"{field_name} يجب أن يكون {min_len} أحرف على الأقل")
        return True
    
    @staticmethod
    def max_length(value, max_len, field_name="الحقل"):
        """التحقق من الحد الأقصى لطول النص"""
        if value and len(str(value)) > max_len:
            raise ValidationError(f"{field_name} يجب أن يكون {max_len} حرف كحد أقصى")
        return True
    
    @staticmethod
    def email(value, field_name="البريد الإلكتروني"):
        """التحقق من صحة البريد الإلكتروني"""
        if not value:
            return True  # اختياري
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(pattern, value):
            raise ValidationError(f"{field_name} غير صحيح")
        return True
    
    @staticmethod
    def phone(value, field_name="رقم الهاتف"):
        """التحقق من صحة رقم الهاتف"""
        if not value:
            return True  # اختياري
        
        # إزالة المسافات والرموز
        clean_phone = re.sub(r'[^\d+]', '', str(value))
        
        # فحص الطول
        if len(clean_phone) < 10 or len(clean_phone) > 15:
            raise ValidationError(f"{field_name} غير صحيح")
        
        # فحص التنسيق
        if not re.match(r'^[\+]?[0-9]{10,15}$', clean_phone):
            raise ValidationError(f"{field_name} غير صحيح")
        
        return True
    
    @staticmethod
    def numeric(value, field_name="الحقل"):
        """التحقق من أن القيمة رقمية"""
        if value is None:
            return True
        
        try:
            float(value)
            return True
        except (ValueError, TypeError):
            raise ValidationError(f"{field_name} يجب أن يكون رقماً")
    
    @staticmethod
    def positive(value, field_name="الحقل"):
        """التحقق من أن القيمة موجبة"""
        if value is None:
            return True
        
        try:
            if float(value) < 0:
                raise ValidationError(f"{field_name} يجب أن يكون رقماً موجباً")
            return True
        except (ValueError, TypeError):
            raise ValidationError(f"{field_name} يجب أن يكون رقماً")
    
    @staticmethod
    def min_value(value, min_val, field_name="الحقل"):
        """التحقق من الحد الأدنى للقيمة"""
        if value is None:
            return True
        
        try:
            if float(value) < min_val:
                raise ValidationError(f"{field_name} يجب أن يكون {min_val} أو أكثر")
            return True
        except (ValueError, TypeError):
            raise ValidationError(f"{field_name} يجب أن يكون رقماً")
    
    @staticmethod
    def max_value(value, max_val, field_name="الحقل"):
        """التحقق من الحد الأقصى للقيمة"""
        if value is None:
            return True
        
        try:
            if float(value) > max_val:
                raise ValidationError(f"{field_name} يجب أن يكون {max_val} أو أقل")
            return True
        except (ValueError, TypeError):
            raise ValidationError(f"{field_name} يجب أن يكون رقماً")
    
    @staticmethod
    def date_format(value, format_str="%Y-%m-%d", field_name="التاريخ"):
        """التحقق من تنسيق التاريخ"""
        if not value:
            return True
        
        try:
            datetime.strptime(str(value), format_str)
            return True
        except ValueError:
            raise ValidationError(f"{field_name} غير صحيح. التنسيق المطلوب: {format_str}")
    
    @staticmethod
    def future_date(value, field_name="التاريخ"):
        """التحقق من أن التاريخ في المستقبل"""
        if not value:
            return True
        
        try:
            if isinstance(value, str):
                date_obj = datetime.strptime(value, "%Y-%m-%d").date()
            elif isinstance(value, datetime):
                date_obj = value.date()
            elif isinstance(value, date):
                date_obj = value
            else:
                raise ValueError("تنسيق تاريخ غير صحيح")
            
            if date_obj <= date.today():
                raise ValidationError(f"{field_name} يجب أن يكون في المستقبل")
            
            return True
        except ValueError:
            raise ValidationError(f"{field_name} غير صحيح")
    
    @staticmethod
    def past_date(value, field_name="التاريخ"):
        """التحقق من أن التاريخ في الماضي"""
        if not value:
            return True
        
        try:
            if isinstance(value, str):
                date_obj = datetime.strptime(value, "%Y-%m-%d").date()
            elif isinstance(value, datetime):
                date_obj = value.date()
            elif isinstance(value, date):
                date_obj = value
            else:
                raise ValueError("تنسيق تاريخ غير صحيح")
            
            if date_obj >= date.today():
                raise ValidationError(f"{field_name} يجب أن يكون في الماضي")
            
            return True
        except ValueError:
            raise ValidationError(f"{field_name} غير صحيح")
    
    @staticmethod
    def choice(value, choices, field_name="الحقل"):
        """التحقق من أن القيمة ضمن الخيارات المحددة"""
        if value is None:
            return True
        
        if value not in choices:
            choices_str = "، ".join(str(c) for c in choices)
            raise ValidationError(f"{field_name} يجب أن يكون أحد القيم التالية: {choices_str}")
        
        return True
    
    @staticmethod
    def unique(value, model_class, field_name, exclude_id=None, field_display_name="الحقل"):
        """التحقق من تفرد القيمة"""
        if not value:
            return True
        
        # البحث عن سجل بنفس القيمة
        existing = model_class.find_all(f"{field_name} = ?", [value])
        
        if existing:
            # إذا كان هناك معرف للاستثناء (في حالة التحديث)
            if exclude_id:
                existing = [record for record in existing if record.id != exclude_id]
            
            if existing:
                raise ValidationError(f"{field_display_name} موجود مسبقاً")
        
        return True
    
    @staticmethod
    def password_strength(value, field_name="كلمة المرور"):
        """التحقق من قوة كلمة المرور"""
        if not value:
            return True
        
        password = str(value)
        errors = []
        
        # الحد الأدنى للطول
        if len(password) < 8:
            errors.append("يجب أن تكون 8 أحرف على الأقل")
        
        # وجود أحرف كبيرة
        if not re.search(r'[A-Z]', password):
            errors.append("يجب أن تحتوي على حرف كبير واحد على الأقل")
        
        # وجود أحرف صغيرة
        if not re.search(r'[a-z]', password):
            errors.append("يجب أن تحتوي على حرف صغير واحد على الأقل")
        
        # وجود أرقام
        if not re.search(r'\d', password):
            errors.append("يجب أن تحتوي على رقم واحد على الأقل")
        
        # وجود رموز خاصة
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("يجب أن تحتوي على رمز خاص واحد على الأقل")
        
        if errors:
            raise ValidationError(f"{field_name} ضعيفة. " + "، ".join(errors))
        
        return True
    
    @staticmethod
    def tax_number(value, field_name="الرقم الضريبي"):
        """التحقق من صحة الرقم الضريبي السعودي"""
        if not value:
            return True
        
        # إزالة المسافات والرموز
        clean_number = re.sub(r'[^\d]', '', str(value))
        
        # فحص الطول (15 رقم للرقم الضريبي السعودي)
        if len(clean_number) != 15:
            raise ValidationError(f"{field_name} يجب أن يكون 15 رقماً")
        
        # فحص أن الرقم يبدأ بـ 3 (للشركات) أو 7 (للأفراد)
        if not clean_number.startswith(('3', '7')):
            raise ValidationError(f"{field_name} غير صحيح")
        
        return True
    
    @staticmethod
    def iban(value, field_name="رقم الآيبان"):
        """التحقق من صحة رقم الآيبان السعودي"""
        if not value:
            return True
        
        # إزالة المسافات
        clean_iban = re.sub(r'\s', '', str(value).upper())
        
        # فحص الطول (24 حرف للآيبان السعودي)
        if len(clean_iban) != 24:
            raise ValidationError(f"{field_name} يجب أن يكون 24 حرفاً")
        
        # فحص أن يبدأ بـ SA
        if not clean_iban.startswith('SA'):
            raise ValidationError(f"{field_name} يجب أن يبدأ بـ SA")
        
        # فحص التنسيق
        if not re.match(r'^SA\d{22}$', clean_iban):
            raise ValidationError(f"{field_name} غير صحيح")
        
        return True

def validate_model(model_instance, rules):
    """التحقق من صحة نموذج البيانات"""
    errors = []
    
    for field_name, field_rules in rules.items():
        value = getattr(model_instance, field_name, None)
        
        for rule in field_rules:
            try:
                rule_name = rule['rule']
                rule_params = rule.get('params', {})
                field_display_name = rule.get('field_name', field_name)
                
                # تطبيق القاعدة
                validator_method = getattr(Validator, rule_name)
                validator_method(value, field_display_name, **rule_params)
                
            except ValidationError as e:
                errors.append(str(e))
            except AttributeError:
                errors.append(f"قاعدة التحقق '{rule_name}' غير موجودة")
    
    return len(errors) == 0, errors

# قواعد التحقق الشائعة
COMMON_VALIDATION_RULES = {
    'user': {
        'username': [
            {'rule': 'required', 'field_name': 'اسم المستخدم'},
            {'rule': 'min_length', 'params': {'min_len': 3}, 'field_name': 'اسم المستخدم'},
            {'rule': 'max_length', 'params': {'max_len': 50}, 'field_name': 'اسم المستخدم'}
        ],
        'full_name': [
            {'rule': 'required', 'field_name': 'الاسم الكامل'},
            {'rule': 'min_length', 'params': {'min_len': 2}, 'field_name': 'الاسم الكامل'}
        ],
        'email': [
            {'rule': 'email', 'field_name': 'البريد الإلكتروني'}
        ]
    },
    'supplier': {
        'name': [
            {'rule': 'required', 'field_name': 'اسم المورد'},
            {'rule': 'min_length', 'params': {'min_len': 2}, 'field_name': 'اسم المورد'}
        ],
        'email': [
            {'rule': 'email', 'field_name': 'البريد الإلكتروني'}
        ],
        'phone': [
            {'rule': 'phone', 'field_name': 'رقم الهاتف'}
        ],
        'credit_limit': [
            {'rule': 'positive', 'field_name': 'حد الائتمان'}
        ]
    },
    'customer': {
        'name': [
            {'rule': 'required', 'field_name': 'اسم العميل'},
            {'rule': 'min_length', 'params': {'min_len': 2}, 'field_name': 'اسم العميل'}
        ],
        'email': [
            {'rule': 'email', 'field_name': 'البريد الإلكتروني'}
        ],
        'phone': [
            {'rule': 'phone', 'field_name': 'رقم الهاتف'}
        ],
        'credit_limit': [
            {'rule': 'positive', 'field_name': 'حد الائتمان'}
        ]
    },
    'product': {
        'code': [
            {'rule': 'required', 'field_name': 'كود المنتج'}
        ],
        'name': [
            {'rule': 'required', 'field_name': 'اسم المنتج'},
            {'rule': 'min_length', 'params': {'min_len': 2}, 'field_name': 'اسم المنتج'}
        ],
        'cost_price': [
            {'rule': 'positive', 'field_name': 'سعر التكلفة'}
        ],
        'selling_price': [
            {'rule': 'positive', 'field_name': 'سعر البيع'}
        ],
        'current_stock': [
            {'rule': 'positive', 'field_name': 'المخزون الحالي'}
        ]
    }
}
