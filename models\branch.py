"""
نموذج الفروع
"""
from models.base_model import BaseModel

class Branch(BaseModel):
    """نموذج الفرع"""
    
    table_name = "branches"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = kwargs.get('name', '')
        self.address = kwargs.get('address', '')
        self.phone = kwargs.get('phone', '')
        self.email = kwargs.get('email', '')
        self.manager_id = kwargs.get('manager_id')
        self.is_active = kwargs.get('is_active', True)
    
    @classmethod
    def get_active_branches(cls):
        """الحصول على الفروع النشطة"""
        return cls.find_all("is_active = 1", order_by="name")
    
    def get_manager(self):
        """الحصول على مدير الفرع"""
        if not self.manager_id:
            return None
        
        from models.user import User
        return User.find_by_id(self.manager_id)
    
    def get_users_count(self):
        """الحصول على عدد المستخدمين في الفرع"""
        from models.user import User
        return User.count("branch_id = ? AND is_active = 1", [self.id])
    
    def get_customers_count(self):
        """الحصول على عدد العملاء في الفرع"""
        from models.customer import Customer
        return Customer.count("branch_id = ? AND is_active = 1", [self.id])
    
    def get_suppliers_count(self):
        """الحصول على عدد الموردين في الفرع"""
        from models.supplier import Supplier
        return Supplier.count("branch_id = ? AND is_active = 1", [self.id])
    
    def validate(self):
        """التحقق من صحة بيانات الفرع"""
        errors = []
        
        # التحقق من الاسم
        if not self.name or len(self.name.strip()) < 2:
            errors.append("اسم الفرع مطلوب ويجب أن يكون حرفين على الأقل")
        
        # التحقق من البريد الإلكتروني
        if self.email:
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, self.email):
                errors.append("البريد الإلكتروني غير صحيح")
        
        return len(errors) == 0, errors
    
    def __str__(self):
        return self.name
