import sqlite3
import os
from datetime import datetime

class DatabaseManager:
    def __init__(self, db_path="database/erp_database.db"):
        self.db_path = db_path
        self.ensure_database_directory()
        self.init_database()
    
    def ensure_database_directory(self):
        """إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً"""
        db_dir = os.path.dirname(self.db_path)
        if not os.path.exists(db_dir):
            os.makedirs(db_dir)
    
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                email TEXT,
                role TEXT NOT NULL DEFAULT 'user',
                permissions TEXT,
                branch_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
        ''')
        
        # جدول الفروع
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS branches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                address TEXT,
                phone TEXT,
                email TEXT,
                manager_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (manager_id) REFERENCES users(id)
            )
        ''')
        
        # جدول الموردين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                contact_person TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                tax_number TEXT,
                payment_terms INTEGER DEFAULT 30,
                credit_limit REAL DEFAULT 0,
                current_balance REAL DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                branch_id INTEGER,
                FOREIGN KEY (branch_id) REFERENCES branches(id)
            )
        ''')
        
        # جدول العملاء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                contact_person TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                tax_number TEXT,
                payment_terms INTEGER DEFAULT 30,
                credit_limit REAL DEFAULT 0,
                current_balance REAL DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                branch_id INTEGER,
                FOREIGN KEY (branch_id) REFERENCES branches(id)
            )
        ''')
        
        # جدول المنتجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                category TEXT,
                unit TEXT DEFAULT 'piece',
                cost_price REAL DEFAULT 0,
                selling_price REAL DEFAULT 0,
                min_stock_level INTEGER DEFAULT 0,
                current_stock INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                branch_id INTEGER,
                FOREIGN KEY (branch_id) REFERENCES branches(id)
            )
        ''')
        
        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                invoice_type TEXT NOT NULL, -- 'purchase', 'sale'
                customer_id INTEGER,
                supplier_id INTEGER,
                invoice_date DATE NOT NULL,
                due_date DATE,
                subtotal REAL DEFAULT 0,
                tax_amount REAL DEFAULT 0,
                discount_amount REAL DEFAULT 0,
                total_amount REAL DEFAULT 0,
                paid_amount REAL DEFAULT 0,
                status TEXT DEFAULT 'pending', -- 'pending', 'paid', 'overdue', 'cancelled'
                notes TEXT,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                branch_id INTEGER,
                FOREIGN KEY (customer_id) REFERENCES customers(id),
                FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (branch_id) REFERENCES branches(id)
            )
        ''')
        
        # جدول تفاصيل الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products(id)
            )
        ''')
        
        # جدول الموظفين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_number TEXT UNIQUE NOT NULL,
                full_name TEXT NOT NULL,
                position TEXT,
                department TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                hire_date DATE,
                salary REAL DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                branch_id INTEGER,
                FOREIGN KEY (branch_id) REFERENCES branches(id)
            )
        ''')
        
        # جدول الحسابات العامة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_code TEXT UNIQUE NOT NULL,
                account_name TEXT NOT NULL,
                account_type TEXT NOT NULL, -- 'asset', 'liability', 'equity', 'revenue', 'expense'
                parent_account_id INTEGER,
                balance REAL DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_account_id) REFERENCES accounts(id)
            )
        ''')
        
        # جدول القيود المحاسبية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS journal_entries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entry_number TEXT UNIQUE NOT NULL,
                entry_date DATE NOT NULL,
                description TEXT,
                reference_type TEXT, -- 'invoice', 'payment', 'adjustment'
                reference_id INTEGER,
                total_debit REAL DEFAULT 0,
                total_credit REAL DEFAULT 0,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                branch_id INTEGER,
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (branch_id) REFERENCES branches(id)
            )
        ''')
        
        # جدول تفاصيل القيود المحاسبية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS journal_entry_details (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                journal_entry_id INTEGER NOT NULL,
                account_id INTEGER NOT NULL,
                debit_amount REAL DEFAULT 0,
                credit_amount REAL DEFAULT 0,
                description TEXT,
                FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
                FOREIGN KEY (account_id) REFERENCES accounts(id)
            )
        ''')
        
        # جدول الإشعارات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                notification_type TEXT DEFAULT 'info', -- 'info', 'warning', 'error', 'success'
                is_read BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ''')
        
        # جدول سجل النشاطات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                table_name TEXT,
                record_id INTEGER,
                old_values TEXT,
                new_values TEXT,
                ip_address TEXT,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ''')
        
        # جدول الإعدادات العامة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT UNIQUE NOT NULL,
                setting_value TEXT,
                setting_type TEXT DEFAULT 'string', -- 'string', 'number', 'boolean', 'json'
                description TEXT,
                updated_by INTEGER,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )
        ''')
        
        # إدراج البيانات الافتراضية
        self.insert_default_data(cursor)
        
        conn.commit()
        conn.close()
    
    def insert_default_data(self, cursor):
        """إدراج البيانات الافتراضية"""
        # إنشاء مستخدم المدير الافتراضي
        import bcrypt
        default_password = "admin123"
        password_hash = bcrypt.hashpw(default_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, password_hash, full_name, role, permissions)
            VALUES (?, ?, ?, ?, ?)
        ''', ('admin', password_hash, 'مدير النظام', 'admin', 'all'))
        
        # إنشاء الفرع الرئيسي
        cursor.execute('''
            INSERT OR IGNORE INTO branches (name, address, is_active)
            VALUES (?, ?, ?)
        ''', ('الفرع الرئيسي', 'العنوان الرئيسي', 1))
        
        # إعدادات النظام الافتراضية
        default_settings = [
            ('company_name', 'اسم الشركة', 'string', 'اسم الشركة'),
            ('company_address', 'عنوان الشركة', 'string', 'عنوان الشركة'),
            ('company_phone', 'هاتف الشركة', 'string', 'رقم هاتف الشركة'),
            ('company_email', 'بريد الشركة', 'string', 'البريد الإلكتروني للشركة'),
            ('default_currency', 'ريال سعودي', 'string', 'العملة الافتراضية'),
            ('tax_rate', '15', 'number', 'معدل الضريبة المضافة'),
            ('backup_frequency', '24', 'number', 'تكرار النسخ الاحتياطي بالساعات'),
            ('language', 'ar', 'string', 'لغة النظام الافتراضية'),
            ('theme', 'light', 'string', 'مظهر النظام'),
            ('auto_backup', 'true', 'boolean', 'تفعيل النسخ الاحتياطي التلقائي')
        ]
        
        for setting in default_settings:
            cursor.execute('''
                INSERT OR IGNORE INTO system_settings (setting_key, setting_value, setting_type, description)
                VALUES (?, ?, ?, ?)
            ''', setting)
        
        # حسابات محاسبية افتراضية
        default_accounts = [
            ('1000', 'الأصول', 'asset', None),
            ('1100', 'الأصول المتداولة', 'asset', 1),
            ('1110', 'النقدية', 'asset', 2),
            ('1120', 'العملاء', 'asset', 2),
            ('1130', 'المخزون', 'asset', 2),
            ('2000', 'الخصوم', 'liability', None),
            ('2100', 'الخصوم المتداولة', 'liability', 6),
            ('2110', 'الموردين', 'liability', 7),
            ('3000', 'حقوق الملكية', 'equity', None),
            ('3100', 'رأس المال', 'equity', 9),
            ('4000', 'الإيرادات', 'revenue', None),
            ('4100', 'إيرادات المبيعات', 'revenue', 11),
            ('5000', 'المصروفات', 'expense', None),
            ('5100', 'تكلفة البضاعة المباعة', 'expense', 13)
        ]
        
        for i, account in enumerate(default_accounts, 1):
            cursor.execute('''
                INSERT OR IGNORE INTO accounts (account_code, account_name, account_type, parent_account_id)
                VALUES (?, ?, ?, ?)
            ''', account)

# إنشاء مثيل واحد من مدير قاعدة البيانات
db_manager = DatabaseManager()
