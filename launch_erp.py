#!/usr/bin/env python3
"""
ملف تشغيل نظام إدارة الأعمال المتكامل - الإصدار النهائي
"""
import sys
import os
import platform
import subprocess
from datetime import datetime

def print_welcome():
    """طباعة رسالة الترحيب"""
    print("\n" + "="*70)
    print("🏢 نظام إدارة الأعمال المتكامل - الإصدار 1.0.0")
    print("   Integrated Business Management System v1.0.0")
    print("="*70)
    print("📅 التاريخ:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("💻 النظام:", platform.system(), platform.release())
    print("🐍 Python:", sys.version.split()[0])
    print("="*70)

def check_system_requirements():
    """فحص متطلبات النظام"""
    print("\n🔍 فحص متطلبات النظام...")
    
    # فحص إصدار Python
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"   الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - متوافق")
    
    # فحص المكتبات الأساسية
    required_packages = {
        'customtkinter': 'واجهة المستخدم الرسومية',
        'matplotlib': 'الرسوم البيانية',
        'pandas': 'معالجة البيانات',
        'bcrypt': 'تشفير كلمات المرور',
        'Pillow': 'معالجة الصور'
    }
    
    missing_packages = []
    
    for package, description in required_packages.items():
        try:
            __import__(package)
            print(f"✅ {package} - {description}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - {description} (غير مثبت)")
    
    if missing_packages:
        print(f"\n⚠️  المكتبات المفقودة: {', '.join(missing_packages)}")
        
        install = input("هل تريد تثبيت المكتبات المفقودة؟ (y/n): ").lower().strip()
        if install in ['y', 'yes', 'نعم']:
            return install_packages()
        else:
            print("❌ لا يمكن تشغيل النظام بدون المكتبات المطلوبة")
            return False
    
    return True

def install_packages():
    """تثبيت المكتبات المطلوبة"""
    print("\n📦 تثبيت المكتبات...")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt", "--upgrade"
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ تم تثبيت جميع المكتبات بنجاح")
            return True
        else:
            print("❌ فشل في تثبيت المكتبات:")
            print(result.stderr)
            return False
    
    except subprocess.TimeoutExpired:
        print("❌ انتهت مهلة تثبيت المكتبات")
        return False
    except Exception as e:
        print(f"❌ خطأ في تثبيت المكتبات: {e}")
        return False

def check_optional_packages():
    """فحص المكتبات الاختيارية"""
    print("\n🔍 فحص المكتبات الاختيارية...")
    
    optional_packages = {
        'arabic_reshaper': 'دعم النص العربي المحسن',
        'bidi': 'دعم اتجاه النص',
        'openpyxl': 'تصدير ملفات Excel',
        'reportlab': 'إنشاء تقارير PDF'
    }
    
    for package, description in optional_packages.items():
        try:
            __import__(package)
            print(f"✅ {package} - {description}")
        except ImportError:
            print(f"⚠️  {package} - {description} (اختياري - غير مثبت)")

def setup_environment():
    """إعداد بيئة العمل"""
    print("\n📁 إعداد بيئة العمل...")
    
    directories = [
        "database",
        "backups", 
        "logs",
        "reports",
        "temp",
        "assets/icons",
        "assets/images", 
        "assets/fonts"
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ {directory}")
        except Exception as e:
            print(f"❌ فشل في إنشاء {directory}: {e}")
            return False
    
    return True

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️  اختبار قاعدة البيانات...")
    
    try:
        from config.database import db_manager
        
        # اختبار الاتصال
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # فحص وجود الجداول الأساسية
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        required_tables = ['users', 'branches', 'suppliers', 'customers', 'products', 'invoices']
        missing_tables = [table for table in required_tables if table not in tables]
        
        if missing_tables:
            print(f"❌ جداول مفقودة: {', '.join(missing_tables)}")
            conn.close()
            return False
        
        # فحص المستخدم الافتراضي
        cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
        admin_count = cursor.fetchone()[0]
        
        if admin_count == 0:
            print("❌ المستخدم الافتراضي غير موجود")
            conn.close()
            return False
        
        conn.close()
        print("✅ قاعدة البيانات جاهزة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def show_system_info():
    """عرض معلومات النظام"""
    print("\n" + "="*70)
    print("📋 معلومات النظام:")
    print(f"   🏢 اسم النظام: نظام إدارة الأعمال المتكامل")
    print(f"   📦 الإصدار: 1.0.0")
    print(f"   👨‍💻 المطور: فريق التطوير")
    print(f"   📅 تاريخ الإصدار: 2024")
    print(f"   🌐 اللغات المدعومة: العربية، الإنجليزية")
    print(f"   💾 قاعدة البيانات: SQLite3")
    print(f"   🖥️  الواجهة: CustomTkinter")
    print("="*70)

def show_login_info():
    """عرض معلومات تسجيل الدخول"""
    print("\n" + "🔐" + " معلومات تسجيل الدخول الافتراضية " + "🔐")
    print("┌" + "─"*40 + "┐")
    print("│  اسم المستخدم: admin" + " "*16 + "│")
    print("│  كلمة المرور: admin123" + " "*15 + "│")
    print("└" + "─"*40 + "┘")
    print("\n💡 نصائح:")
    print("   • يمكنك تغيير كلمة المرور من إعدادات النظام")
    print("   • يمكنك إنشاء مستخدمين جدد من قائمة إدارة المستخدمين")
    print("   • تأكد من إنشاء نسخة احتياطية بانتظام")

def start_application():
    """تشغيل التطبيق"""
    print("\n🚀 تشغيل النظام...")
    print("⏳ يرجى الانتظار...")
    
    try:
        # تشغيل التطبيق الرئيسي
        import main
        main.main()
        
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف النظام بواسطة المستخدم")
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        print("\n🔧 تفاصيل الخطأ:")
        import traceback
        traceback.print_exc()
        
        print("\n💡 اقتراحات لحل المشكلة:")
        print("   1. تأكد من تثبيت جميع المكتبات المطلوبة")
        print("   2. تأكد من وجود صلاحيات الكتابة في مجلد النظام")
        print("   3. جرب تشغيل النظام كمدير")
        print("   4. تحقق من ملفات السجلات في مجلد logs")

def main():
    """الدالة الرئيسية"""
    # تعيين ترميز UTF-8
    if platform.system() == "Windows":
        try:
            os.system("chcp 65001 >nul 2>&1")
        except:
            pass
    
    # طباعة رسالة الترحيب
    print_welcome()
    
    # فحص متطلبات النظام
    if not check_system_requirements():
        input("\n❌ فشل في فحص المتطلبات. اضغط Enter للخروج...")
        return 1
    
    # فحص المكتبات الاختيارية
    check_optional_packages()
    
    # إعداد بيئة العمل
    if not setup_environment():
        input("\n❌ فشل في إعداد بيئة العمل. اضغط Enter للخروج...")
        return 1
    
    # اختبار قاعدة البيانات
    if not test_database():
        input("\n❌ فشل في اختبار قاعدة البيانات. اضغط Enter للخروج...")
        return 1
    
    # عرض معلومات النظام
    show_system_info()
    
    # عرض معلومات تسجيل الدخول
    show_login_info()
    
    # تأكيد التشغيل
    print("\n" + "="*70)
    confirm = input("هل تريد تشغيل النظام الآن؟ (y/n): ").lower().strip()
    
    if confirm in ['y', 'yes', 'نعم', '']:
        # تشغيل التطبيق
        start_application()
    else:
        print("تم إلغاء تشغيل النظام")
    
    print("\n👋 شكراً لاستخدام نظام إدارة الأعمال المتكامل")
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
