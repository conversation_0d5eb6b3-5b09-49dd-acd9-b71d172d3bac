#!/usr/bin/env python3
"""
ملف تشغيل مبسط لنظام إدارة الأعمال المتكامل
"""
import subprocess
import sys
import os

def install_requirements():
    """تثبيت المتطلبات"""
    print("جاري تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("تم تثبيت المتطلبات بنجاح!")
        return True
    except subprocess.CalledProcessError:
        print("فشل في تثبيت المتطلبات!")
        return False

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 8):
        print("خطأ: يتطلب Python 3.8 أو أحدث")
        return False
    return True

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("نظام إدارة الأعمال المتكامل")
    print("=" * 50)
    
    # فحص إصدار Python
    if not check_python_version():
        return
    
    # فحص وجود ملف المتطلبات
    if not os.path.exists("requirements.txt"):
        print("خطأ: ملف requirements.txt غير موجود")
        return
    
    # سؤال المستخدم عن تثبيت المتطلبات
    install = input("هل تريد تثبيت المتطلبات؟ (y/n): ").lower().strip()
    if install in ['y', 'yes', 'نعم']:
        if not install_requirements():
            return
    
    # تشغيل التطبيق
    print("\nجاري تشغيل النظام...")
    print("بيانات الدخول الافتراضية:")
    print("اسم المستخدم: admin")
    print("كلمة المرور: admin123")
    print("-" * 30)
    
    try:
        import main
        main.main()
    except KeyboardInterrupt:
        print("\nتم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"خطأ في تشغيل النظام: {e}")

if __name__ == "__main__":
    main()
