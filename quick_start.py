#!/usr/bin/env python3
"""
تشغيل سريع للنظام - للاختبار
"""
import sys
import os

def main():
    print("🚀 تشغيل سريع لنظام إدارة الأعمال المتكامل")
    print("="*50)
    
    try:
        # فحص المكتبات الأساسية
        import customtkinter
        import matplotlib
        import pandas
        import bcrypt
        print("✅ جميع المكتبات الأساسية متوفرة")
        
        # فحص قاعدة البيانات
        from config.database import db_manager
        print("✅ قاعدة البيانات جاهزة")
        
        # فحص المستخدم الافتراضي
        from models.user import User
        admin_user = User.find_by_username("admin")
        if admin_user:
            print("✅ المستخدم الافتراضي موجود")
        else:
            print("❌ المستخدم الافتراضي غير موجود")
        
        print("\n🔐 معلومات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        
        print("\n🚀 تشغيل النظام...")
        
        # تشغيل النظام
        import main
        main.main()
        
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("يرجى تثبيت المتطلبات: pip install -r requirements.txt")
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
