"""
النموذج الأساسي لجميع النماذج في النظام
"""
from datetime import datetime
import json
from config.database import db_manager

class BaseModel:
    """النموذج الأساسي لجميع النماذج"""

    table_name = None

    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.created_at = kwargs.get('created_at')
        self.updated_at = kwargs.get('updated_at')

        # تعيين الخصائص الأخرى
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

    @classmethod
    def get_connection(cls):
        """الحصول على اتصال قاعدة البيانات"""
        return db_manager.get_connection()

    @classmethod
    def find_by_id(cls, record_id):
        """البحث عن سجل بواسطة المعرف"""
        if not cls.table_name:
            raise ValueError("table_name must be defined")

        conn = cls.get_connection()
        cursor = conn.cursor()
        cursor.execute(f"SELECT * FROM {cls.table_name} WHERE id = ?", (record_id,))
        result = cursor.fetchone()
        conn.close()

        if result:
            return cls(**dict(result))
        return None

    @classmethod
    def find_all(cls, where_clause="", params=None, order_by="id DESC", limit=None):
        """البحث عن جميع السجلات"""
        if not cls.table_name:
            raise ValueError("table_name must be defined")

        query = f"SELECT * FROM {cls.table_name}"

        if where_clause:
            query += f" WHERE {where_clause}"

        if order_by:
            query += f" ORDER BY {order_by}"

        if limit:
            query += f" LIMIT {limit}"

        conn = cls.get_connection()
        cursor = conn.cursor()
        cursor.execute(query, params or [])
        results = cursor.fetchall()
        conn.close()

        return [cls(**dict(row)) for row in results]

    @classmethod
    def count(cls, where_clause="", params=None):
        """عد السجلات"""
        if not cls.table_name:
            raise ValueError("table_name must be defined")

        query = f"SELECT COUNT(*) as count FROM {cls.table_name}"

        if where_clause:
            query += f" WHERE {where_clause}"

        conn = cls.get_connection()
        cursor = conn.cursor()
        cursor.execute(query, params or [])
        result = cursor.fetchone()
        conn.close()

        return result['count'] if result else 0

    def save(self):
        """حفظ السجل (إنشاء أو تحديث)"""
        if self.id:
            return self.update()
        else:
            return self.create()

    def create(self):
        """إنشاء سجل جديد"""
        if not self.table_name:
            raise ValueError("table_name must be defined")

        # الحصول على الحقول والقيم
        fields, values = self._get_fields_and_values(exclude_id=True)

        if not fields:
            return False

        # إنشاء الاستعلام
        placeholders = ', '.join(['?' for _ in fields])
        query = f"INSERT INTO {self.table_name} ({', '.join(fields)}) VALUES ({placeholders})"

        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(query, values)
            self.id = cursor.lastrowid
            conn.commit()
            conn.close()

            # تسجيل النشاط (معطل مؤقتاً)
            # self._log_activity('CREATE')

            return True
        except Exception as e:
            print(f"Error creating record: {e}")
            return False

    def update(self):
        """تحديث السجل"""
        if not self.id or not self.table_name:
            return False

        # الحصول على القيم القديمة للمقارنة
        old_record = self.find_by_id(self.id)
        old_values = old_record.to_dict() if old_record else {}

        # الحصول على الحقول والقيم
        fields, values = self._get_fields_and_values(exclude_id=True)

        if not fields:
            return False

        # إنشاء الاستعلام
        set_clause = ', '.join([f"{field} = ?" for field in fields])
        query = f"UPDATE {self.table_name} SET {set_clause} WHERE id = ?"
        values.append(self.id)

        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(query, values)
            conn.commit()
            conn.close()

            # تسجيل النشاط (معطل مؤقتاً)
            # self._log_activity('UPDATE', old_values, self.to_dict())

            return True
        except Exception as e:
            print(f"Error updating record: {e}")
            return False

    def delete(self):
        """حذف السجل"""
        if not self.id or not self.table_name:
            return False

        # الحصول على القيم القديمة
        old_values = self.to_dict()

        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(f"DELETE FROM {self.table_name} WHERE id = ?", (self.id,))
            conn.commit()
            conn.close()

            # تسجيل النشاط (معطل مؤقتاً)
            # self._log_activity('DELETE', old_values)

            return True
        except Exception as e:
            print(f"Error deleting record: {e}")
            return False

    def _get_fields_and_values(self, exclude_id=False):
        """الحصول على الحقول والقيم للحفظ"""
        fields = []
        values = []

        # استخدام get_db_fields() إذا كانت متوفرة
        if hasattr(self, 'get_db_fields'):
            db_fields = self.get_db_fields()
            for field_name in db_fields:
                if exclude_id and field_name == 'id':
                    continue

                if hasattr(self, field_name):
                    value = getattr(self, field_name)
                    if value is not None:
                        fields.append(field_name)
                        values.append(value)
        else:
            # الطريقة القديمة مع تصفية أفضل
            excluded_attrs = {'table_name'}

            for attr_name, value in self.__dict__.items():
                if attr_name.startswith('_'):
                    continue

                if attr_name in excluded_attrs:
                    continue

                if exclude_id and attr_name == 'id':
                    continue

                if value is not None:
                    fields.append(attr_name)
                    values.append(value)

        return fields, values

    def to_dict(self):
        """تحويل الكائن إلى قاموس"""
        result = {}

        # استخدام __dict__ للحصول على الخصائص الفعلية فقط
        for attr_name, value in self.__dict__.items():
            if not attr_name.startswith('_') and value is not None:
                result[attr_name] = value

        return result

    def _log_activity(self, action, old_values=None, new_values=None):
        """تسجيل النشاط في سجل المراجعة"""
        try:
            # تجنب الاستيراد الدائري وتسجيل سجل المراجعة لنفسه
            if not hasattr(self, 'table_name') or self.table_name == 'audit_log':
                return

            # إنشاء سجل المراجعة مباشرة في قاعدة البيانات
            conn = self.get_connection()
            cursor = conn.cursor()

            # الحصول على المستخدم الحالي
            current_user_id = None
            try:
                from controllers.auth_controller import auth_controller
                if auth_controller.current_user:
                    current_user_id = auth_controller.current_user.id
            except:
                pass

            cursor.execute("""
                INSERT INTO audit_log (user_id, action, table_name, record_id, old_values, new_values, created_at)
                VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (
                current_user_id,
                action,
                self.table_name,
                self.id,
                json.dumps(old_values, default=str) if old_values else None,
                json.dumps(new_values, default=str) if new_values else None
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            # تجاهل أخطاء سجل المراجعة لتجنب تعطيل العمليات الأساسية
            pass

    @classmethod
    def search(cls, search_term, search_fields=None):
        """البحث في السجلات"""
        if not cls.table_name or not search_term:
            return []

        if not search_fields:
            search_fields = ['name']  # حقل افتراضي للبحث

        # إنشاء شروط البحث
        conditions = []
        params = []

        for field in search_fields:
            conditions.append(f"{field} LIKE ?")
            params.append(f"%{search_term}%")

        where_clause = " OR ".join(conditions)

        return cls.find_all(where_clause, params)

    @classmethod
    def paginate(cls, page=1, per_page=20, where_clause="", params=None, order_by="id DESC"):
        """تقسيم النتائج إلى صفحات"""
        offset = (page - 1) * per_page

        # الحصول على إجمالي عدد السجلات
        total_count = cls.count(where_clause, params)

        # الحصول على السجلات للصفحة الحالية
        query = f"SELECT * FROM {cls.table_name}"

        if where_clause:
            query += f" WHERE {where_clause}"

        if order_by:
            query += f" ORDER BY {order_by}"

        query += f" LIMIT {per_page} OFFSET {offset}"

        conn = cls.get_connection()
        cursor = conn.cursor()
        cursor.execute(query, params or [])
        results = cursor.fetchall()
        conn.close()

        records = [cls(**dict(row)) for row in results]

        # حساب معلومات التقسيم
        total_pages = (total_count + per_page - 1) // per_page
        has_prev = page > 1
        has_next = page < total_pages

        return {
            'records': records,
            'total_count': total_count,
            'total_pages': total_pages,
            'current_page': page,
            'per_page': per_page,
            'has_prev': has_prev,
            'has_next': has_next
        }

    def validate(self):
        """التحقق من صحة البيانات"""
        # يمكن تخصيص هذه الدالة في النماذج الفرعية
        return True, []

    def __str__(self):
        """تمثيل نصي للكائن"""
        return f"{self.__class__.__name__}(id={self.id})"

    def __repr__(self):
        """تمثيل للكائن للتطوير"""
        return self.__str__()
