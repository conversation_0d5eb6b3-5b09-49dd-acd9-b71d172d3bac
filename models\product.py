"""
نموذج المنتجات
"""
from models.base_model import BaseModel

class Product(BaseModel):
    """نموذج المنتج"""
    
    table_name = "products"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.code = kwargs.get('code', '')
        self.name = kwargs.get('name', '')
        self.description = kwargs.get('description', '')
        self.category = kwargs.get('category', '')
        self.unit = kwargs.get('unit', 'piece')
        self.cost_price = kwargs.get('cost_price', 0.0)
        self.selling_price = kwargs.get('selling_price', 0.0)
        self.min_stock_level = kwargs.get('min_stock_level', 0)
        self.current_stock = kwargs.get('current_stock', 0)
        self.is_active = kwargs.get('is_active', True)
        self.branch_id = kwargs.get('branch_id')
    
    @classmethod
    def search_products(cls, search_term):
        """البحث في المنتجات"""
        return cls.search(search_term, ['code', 'name', 'description', 'category'])
    
    @classmethod
    def get_active_products(cls):
        """الحصول على المنتجات النشطة"""
        return cls.find_all("is_active = 1", order_by="name")
    
    @classmethod
    def get_products_by_category(cls, category):
        """الحصول على منتجات فئة معينة"""
        return cls.find_all("category = ? AND is_active = 1", [category], order_by="name")
    
    @classmethod
    def get_low_stock_products(cls):
        """الحصول على المنتجات منخفضة المخزون"""
        return cls.find_all("current_stock <= min_stock_level AND is_active = 1", order_by="name")
    
    @classmethod
    def get_out_of_stock_products(cls):
        """الحصول على المنتجات نفدت من المخزون"""
        return cls.find_all("current_stock = 0 AND is_active = 1", order_by="name")
    
    @classmethod
    def find_by_code(cls, code):
        """البحث عن منتج بواسطة الكود"""
        results = cls.find_all("code = ?", [code])
        return results[0] if results else None
    
    def update_stock(self, quantity, operation='add'):
        """تحديث المخزون"""
        if operation == 'add':
            self.current_stock += quantity
        elif operation == 'subtract':
            self.current_stock = max(0, self.current_stock - quantity)
        elif operation == 'set':
            self.current_stock = max(0, quantity)
        
        return self.update()
    
    def is_low_stock(self):
        """فحص ما إذا كان المنتج منخفض المخزون"""
        return self.current_stock <= self.min_stock_level
    
    def is_out_of_stock(self):
        """فحص ما إذا كان المنتج نفد من المخزون"""
        return self.current_stock == 0
    
    def get_profit_margin(self):
        """حساب هامش الربح"""
        if self.cost_price > 0:
            return ((self.selling_price - self.cost_price) / self.cost_price) * 100
        return 0.0
    
    def get_profit_amount(self):
        """حساب مبلغ الربح للوحدة الواحدة"""
        return self.selling_price - self.cost_price
    
    def get_stock_value(self):
        """حساب قيمة المخزون الحالي"""
        return self.current_stock * self.cost_price
    
    def get_total_sold(self):
        """الحصول على إجمالي الكمية المباعة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT COALESCE(SUM(ii.quantity), 0) as total_sold
            FROM invoice_items ii
            JOIN invoices i ON ii.invoice_id = i.id
            WHERE ii.product_id = ? AND i.invoice_type = 'sale'
        """, (self.id,))
        result = cursor.fetchone()
        conn.close()
        return result['total_sold'] if result else 0
    
    def get_total_purchased(self):
        """الحصول على إجمالي الكمية المشتراة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT COALESCE(SUM(ii.quantity), 0) as total_purchased
            FROM invoice_items ii
            JOIN invoices i ON ii.invoice_id = i.id
            WHERE ii.product_id = ? AND i.invoice_type = 'purchase'
        """, (self.id,))
        result = cursor.fetchone()
        conn.close()
        return result['total_purchased'] if result else 0
    
    def validate(self):
        """التحقق من صحة بيانات المنتج"""
        errors = []
        
        # التحقق من الكود
        if not self.code or len(self.code.strip()) < 1:
            errors.append("كود المنتج مطلوب")
        else:
            # التحقق من تفرد الكود
            existing_product = self.find_by_code(self.code)
            if existing_product and existing_product.id != self.id:
                errors.append("كود المنتج موجود مسبقاً")
        
        # التحقق من الاسم
        if not self.name or len(self.name.strip()) < 2:
            errors.append("اسم المنتج مطلوب ويجب أن يكون حرفين على الأقل")
        
        # التحقق من الأسعار
        if self.cost_price < 0:
            errors.append("سعر التكلفة يجب أن يكون رقماً موجباً")
        
        if self.selling_price < 0:
            errors.append("سعر البيع يجب أن يكون رقماً موجباً")
        
        if self.selling_price < self.cost_price:
            errors.append("تحذير: سعر البيع أقل من سعر التكلفة")
        
        # التحقق من المخزون
        if self.current_stock < 0:
            errors.append("المخزون الحالي يجب أن يكون رقماً موجباً")
        
        if self.min_stock_level < 0:
            errors.append("الحد الأدنى للمخزون يجب أن يكون رقماً موجباً")
        
        return len(errors) == 0, errors
    
    def __str__(self):
        return f"{self.code} - {self.name}"
