"""
تحكم النسخ الاحتياطي
"""
import os
import shutil
import sqlite3
import zipfile
from datetime import datetime, timedelta
import threading
import time
from config.settings import DATABASE_PATH, BACKUP_DIR, BACKUP_CONFIG
from utils.logger import system_logger

class BackupController:
    """تحكم النسخ الاحتياطي"""
    
    def __init__(self):
        self.backup_thread = None
        self.is_running = False
        self.ensure_backup_directory()
    
    def ensure_backup_directory(self):
        """التأكد من وجود مجلد النسخ الاحتياطية"""
        os.makedirs(BACKUP_DIR, exist_ok=True)
    
    def create_backup(self, backup_name=None, include_logs=False):
        """إنشاء نسخة احتياطية"""
        try:
            # إنشاء اسم النسخة الاحتياطية
            if not backup_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"backup_{timestamp}"
            
            backup_path = os.path.join(BACKUP_DIR, f"{backup_name}.zip")
            
            # إنشاء ملف ZIP
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # إضافة قاعدة البيانات
                if os.path.exists(DATABASE_PATH):
                    zipf.write(DATABASE_PATH, "database/erp_database.db")
                
                # إضافة ملفات الإعدادات
                config_dir = "config"
                if os.path.exists(config_dir):
                    for root, dirs, files in os.walk(config_dir):
                        for file in files:
                            if file.endswith('.py'):
                                file_path = os.path.join(root, file)
                                arcname = os.path.relpath(file_path)
                                zipf.write(file_path, arcname)
                
                # إضافة السجلات إذا طُلب ذلك
                if include_logs:
                    logs_dir = "logs"
                    if os.path.exists(logs_dir):
                        for root, dirs, files in os.walk(logs_dir):
                            for file in files:
                                if file.endswith('.log'):
                                    file_path = os.path.join(root, file)
                                    arcname = os.path.relpath(file_path)
                                    zipf.write(file_path, arcname)
                
                # إضافة معلومات النسخة الاحتياطية
                backup_info = {
                    'created_at': datetime.now().isoformat(),
                    'database_size': os.path.getsize(DATABASE_PATH) if os.path.exists(DATABASE_PATH) else 0,
                    'include_logs': include_logs,
                    'version': '1.0.0'
                }
                
                import json
                zipf.writestr("backup_info.json", json.dumps(backup_info, indent=2))
            
            # تسجيل النشاط
            system_logger.log_system_event(
                "BACKUP_CREATED",
                f"Backup created: {backup_name} ({self.get_file_size(backup_path)})"
            )
            
            # تنظيف النسخ القديمة
            self.cleanup_old_backups()
            
            return True, backup_path
        
        except Exception as e:
            system_logger.log_error_with_context(e, "Creating backup")
            return False, str(e)
    
    def restore_backup(self, backup_path):
        """استعادة نسخة احتياطية"""
        try:
            if not os.path.exists(backup_path):
                return False, "ملف النسخة الاحتياطية غير موجود"
            
            # إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة
            current_backup_name = f"before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.create_backup(current_backup_name)
            
            # استخراج النسخة الاحتياطية
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                # التحقق من صحة النسخة الاحتياطية
                if "backup_info.json" not in zipf.namelist():
                    return False, "ملف النسخة الاحتياطية غير صحيح"
                
                # قراءة معلومات النسخة الاحتياطية
                backup_info_content = zipf.read("backup_info.json")
                backup_info = json.loads(backup_info_content)
                
                # استخراج قاعدة البيانات
                if "database/erp_database.db" in zipf.namelist():
                    # إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
                    os.makedirs(os.path.dirname(DATABASE_PATH), exist_ok=True)
                    
                    # استخراج قاعدة البيانات
                    with zipf.open("database/erp_database.db") as source:
                        with open(DATABASE_PATH, "wb") as target:
                            shutil.copyfileobj(source, target)
                
                # استخراج السجلات إذا كانت موجودة
                log_files = [f for f in zipf.namelist() if f.startswith("logs/") and f.endswith(".log")]
                if log_files:
                    for log_file in log_files:
                        target_path = log_file
                        os.makedirs(os.path.dirname(target_path), exist_ok=True)
                        zipf.extract(log_file, ".")
            
            # تسجيل النشاط
            system_logger.log_system_event(
                "BACKUP_RESTORED",
                f"Backup restored from: {os.path.basename(backup_path)}"
            )
            
            return True, "تم استعادة النسخة الاحتياطية بنجاح"
        
        except Exception as e:
            system_logger.log_error_with_context(e, "Restoring backup")
            return False, str(e)
    
    def get_backup_list(self):
        """الحصول على قائمة النسخ الاحتياطية"""
        backups = []
        
        if not os.path.exists(BACKUP_DIR):
            return backups
        
        for filename in os.listdir(BACKUP_DIR):
            if filename.endswith('.zip'):
                filepath = os.path.join(BACKUP_DIR, filename)
                
                try:
                    # قراءة معلومات النسخة الاحتياطية
                    backup_info = self.get_backup_info(filepath)
                    
                    backup_data = {
                        'filename': filename,
                        'filepath': filepath,
                        'size': os.path.getsize(filepath),
                        'created_at': datetime.fromtimestamp(os.path.getctime(filepath)),
                        'info': backup_info
                    }
                    
                    backups.append(backup_data)
                
                except Exception as e:
                    # إضافة النسخة الاحتياطية حتى لو فشل في قراءة المعلومات
                    backup_data = {
                        'filename': filename,
                        'filepath': filepath,
                        'size': os.path.getsize(filepath),
                        'created_at': datetime.fromtimestamp(os.path.getctime(filepath)),
                        'info': None,
                        'error': str(e)
                    }
                    backups.append(backup_data)
        
        # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
        backups.sort(key=lambda x: x['created_at'], reverse=True)
        
        return backups
    
    def get_backup_info(self, backup_path):
        """الحصول على معلومات النسخة الاحتياطية"""
        try:
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                if "backup_info.json" in zipf.namelist():
                    backup_info_content = zipf.read("backup_info.json")
                    return json.loads(backup_info_content)
            return None
        except:
            return None
    
    def delete_backup(self, backup_path):
        """حذف نسخة احتياطية"""
        try:
            if os.path.exists(backup_path):
                os.remove(backup_path)
                
                system_logger.log_system_event(
                    "BACKUP_DELETED",
                    f"Backup deleted: {os.path.basename(backup_path)}"
                )
                
                return True
            return False
        except Exception as e:
            system_logger.log_error_with_context(e, "Deleting backup")
            return False
    
    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            max_backups = BACKUP_CONFIG.get('max_backup_files', 30)
            backups = self.get_backup_list()
            
            if len(backups) > max_backups:
                # حذف النسخ الزائدة (الأقدم)
                backups_to_delete = backups[max_backups:]
                
                for backup in backups_to_delete:
                    self.delete_backup(backup['filepath'])
                
                system_logger.log_system_event(
                    "BACKUP_CLEANUP",
                    f"Deleted {len(backups_to_delete)} old backups"
                )
        
        except Exception as e:
            system_logger.log_error_with_context(e, "Cleaning up old backups")
    
    def start_auto_backup(self):
        """بدء النسخ الاحتياطي التلقائي"""
        if not BACKUP_CONFIG.get('auto_backup', True):
            return
        
        if self.backup_thread and self.backup_thread.is_alive():
            return
        
        self.is_running = True
        self.backup_thread = threading.Thread(target=self._auto_backup_worker, daemon=True)
        self.backup_thread.start()
        
        system_logger.log_system_event("AUTO_BACKUP_STARTED", "Auto backup service started")
    
    def stop_auto_backup(self):
        """إيقاف النسخ الاحتياطي التلقائي"""
        self.is_running = False
        
        if self.backup_thread:
            self.backup_thread.join(timeout=5)
        
        system_logger.log_system_event("AUTO_BACKUP_STOPPED", "Auto backup service stopped")
    
    def _auto_backup_worker(self):
        """عامل النسخ الاحتياطي التلقائي"""
        frequency_hours = BACKUP_CONFIG.get('backup_frequency_hours', 24)
        sleep_seconds = frequency_hours * 3600
        
        while self.is_running:
            try:
                # انتظار الفترة المحددة
                for _ in range(sleep_seconds):
                    if not self.is_running:
                        break
                    time.sleep(1)
                
                if self.is_running:
                    # إنشاء نسخة احتياطية تلقائية
                    backup_name = f"auto_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    success, result = self.create_backup(backup_name)
                    
                    if success:
                        system_logger.log_system_event(
                            "AUTO_BACKUP_CREATED",
                            f"Auto backup created: {backup_name}"
                        )
                    else:
                        system_logger.log_error_with_context(
                            result,
                            "Auto backup failed"
                        )
            
            except Exception as e:
                system_logger.log_error_with_context(e, "Auto backup worker")
                time.sleep(300)  # انتظار 5 دقائق قبل المحاولة مرة أخرى
    
    def verify_backup(self, backup_path):
        """التحقق من صحة النسخة الاحتياطية"""
        try:
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                # فحص سلامة الملف المضغوط
                bad_file = zipf.testzip()
                if bad_file:
                    return False, f"ملف تالف في النسخة الاحتياطية: {bad_file}"
                
                # التحقق من وجود قاعدة البيانات
                if "database/erp_database.db" not in zipf.namelist():
                    return False, "قاعدة البيانات غير موجودة في النسخة الاحتياطية"
                
                # التحقق من معلومات النسخة الاحتياطية
                if "backup_info.json" not in zipf.namelist():
                    return False, "معلومات النسخة الاحتياطية غير موجودة"
                
                # قراءة ومعالجة معلومات النسخة الاحتياطية
                backup_info_content = zipf.read("backup_info.json")
                backup_info = json.loads(backup_info_content)
                
                return True, "النسخة الاحتياطية صحيحة"
        
        except zipfile.BadZipFile:
            return False, "ملف النسخة الاحتياطية تالف"
        except Exception as e:
            return False, f"خطأ في التحقق من النسخة الاحتياطية: {str(e)}"
    
    def get_file_size(self, filepath):
        """الحصول على حجم الملف مُنسق"""
        try:
            size = os.path.getsize(filepath)
            
            for unit in ['B', 'KB', 'MB', 'GB']:
                if size < 1024.0:
                    return f"{size:.1f} {unit}"
                size /= 1024.0
            
            return f"{size:.1f} TB"
        except:
            return "غير معروف"
    
    def export_data(self, export_path, tables=None):
        """تصدير البيانات إلى ملفات منفصلة"""
        try:
            os.makedirs(export_path, exist_ok=True)
            
            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(DATABASE_PATH)
            conn.row_factory = sqlite3.Row
            
            # الحصول على قائمة الجداول
            if not tables:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
            
            exported_files = []
            
            for table in tables:
                try:
                    # تصدير الجدول إلى CSV
                    cursor = conn.cursor()
                    cursor.execute(f"SELECT * FROM {table}")
                    rows = cursor.fetchall()
                    
                    if rows:
                        import csv
                        csv_path = os.path.join(export_path, f"{table}.csv")
                        
                        with open(csv_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                            writer = csv.writer(csvfile)
                            
                            # كتابة العناوين
                            writer.writerow([description[0] for description in cursor.description])
                            
                            # كتابة البيانات
                            for row in rows:
                                writer.writerow(row)
                        
                        exported_files.append(csv_path)
                
                except Exception as e:
                    system_logger.log_error_with_context(e, f"Exporting table {table}")
            
            conn.close()
            
            return True, exported_files
        
        except Exception as e:
            system_logger.log_error_with_context(e, "Exporting data")
            return False, str(e)

# إنشاء مثيل واحد من تحكم النسخ الاحتياطي
backup_controller = BackupController()
